﻿:root {
    --primary-color: #007bff;
    --border-color: #dee2e6;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --text-dark: #343a40;
    --text-light: #6c757d;
    --status-active: #28a745;
    --status-inactive: #dc3545;
}

body, html {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    background-color: #f0f2f5;
    height: 100%;
    overflow: hidden;
}

.main-container {
    display: flex;
    height: 100vh;
}

/* --- Left Panel: List View --- */
.list-panel {
    width: 430px;
    background-color: var(--bg-white);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
    background:lightblue;
}

    .list-header h2 {
        margin: 0;
        font-size: 1.25rem;
    }

.add-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
}

.list-body {
    flex-grow: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.list-item {
    padding: 1rem;
    border-left: 4px solid var(--border-color);
    margin: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    background-color: var(--bg-light);
    transition: background-color 0.2s ease, border-left-color 0.2s ease;
}

    .list-item.active {
        border-left-color: var(--primary-color);
        background-color: #e9f2ff;
    }

.list-item-header, .list-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.list-item-header {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.list-item-footer {
    font-size: 0.8rem;
    color: var(--text-light);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 5px;
}

.status-active {
    background-color: var(--status-active);
}

.status-inactive {
    background-color: var(--status-inactive);
}

.list-footer {
    border-top: 1px solid var(--border-color);
    text-align: right;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 5px;
    text-align: right;
}

/* --- Right Panel: Detail View --- */
.detail-panel {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.detail-header {
    background-color: #ebebeb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.search-bar {
    position: relative;
    flex-grow: 1;
    margin-right: 1.5rem;
}

    .search-bar input {
        width: 40%;
        padding: 0.6rem 1rem 0.6rem 2.5rem;
        border-radius: 10px;
        border: 1px solid var(--border-color);
        font-size: 1rem;
    }

    .search-bar .fa-search {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-light);
    }

.action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background-color:lightblue;
    border-radius: 5px;
    cursor: pointer;
    margin-left: 10px;
    font-weight:bold;
}

.detail-body {
    flex-grow: 1;
    background-color: var(--bg-white);
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow-y: auto;
}

.placeholder-text {
    color: var(--text-light);
    font-style: italic;
}

/* Form Styles */
.vehicle-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-light);
    margin-bottom: 0.3rem;
    font-weight: bold;
}
.form-group actBtn {
    display: block;
    font-size: 0.8rem;
    color: var(--text-light);
    margin-bottom: 0.3rem;
    font-weight: bold;
}
   

.form-group input, .form-group select {
    width: 90%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}
.form-group inputnew {
    width: 50%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}


    .form-group input:read-only {
        background-color: var(--bg-light);
        cursor: not-allowed;
    }

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.select-all-container {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.25rem;
    font-weight: bold;
}

    .select-all-container input {
        width: 18px;
        height: 18px;
    }

/* Toast Notifications */
#toastContainer {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

.toast {
    background-color: #333;
    color: #fff;
    padding: 15px 20px;
    border-radius: 5px;
    margin-bottom: 10px;
    opacity: 0;
    transition: opacity 0.5s ease, transform 0.5s ease;
    transform: translateX(100%);
}

    .toast.show {
        opacity: 1;
        transform: translateX(0);
    }

    .toast.success {
        background-color: #28a745;
    }

    .toast.error {
        background-color: #dc3545;
    }


.pagination-btn {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-white);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

    .pagination-btn:hover:not(:disabled) {
        background-color: #f0f0f0;
    }

    .pagination-btn.active {
        background-color: #000000;
        color: var(--bg-white);
        border-color: var(--primary-color);
    }

    .pagination-btn:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }

.validation-error {
    color: #dc3545; /* Red */
    font-size: 0.8rem;
    margin-top: 5px;
}

.Warranty-text{
    Font-weight:bold;
}

.required-indicator {
    color: #dc3545; 
    margin-left: 4px;
}


.input-validation-error {
    border-color: #dc3545 !important;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-text {
    font-size: 14px;
    color: var(--text-light);
    margin: 0 5px;
}

.page-input {
    width: 60px;
    text-align: center;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.view-toggles {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    overflow: hidden;
}

    .view-toggles .view-btn {
        padding: 8px 15px;
        background-color: var(--bg-white);
        border: none;
        cursor: pointer;
        border-right: 1px solid var(--border-color);
    }

        .view-toggles .view-btn:last-child {
            border-right: none;
        }

        .view-toggles .view-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

/* --- Table View --- */
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-white);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    overflow: hidden;
}

    .table th, .table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .table th {
        background-color: var(--bg-light);
        font-size: 0.8rem;
        text-transform: uppercase;
        color: var(--text-light);
    }

.action-icon {
    color: var(--text-light);
    cursor: pointer;
    font-size: 1rem;
    margin: 0 5px;
    transition: color 0.2s;
}

    .action-icon:hover {
        color: var(--primary-color);
    }

/* --- Card View --- */
.card-view-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
}

.vehicle-card {
    background: var(--bg-white);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-top: 4px solid var(--primary-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.card-body {
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    background: var(--bg-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--border-color);
}

.status-pill {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

    .status-pill.in-service {
        background-color: #e7f3ff;
        color: #007bff;
    }

    .status-pill.scrap {
        background-color: #f8d7da;
        color: #721c24;
    }

.actions-group {
    display: flex;
    align-items: center;
    gap: 15px;
}

.view-toggles {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    overflow: hidden;
}

    .view-toggles .view-btn {
        padding: 8px 15px;
        background-color: var(--bg-white);
        border: none;
        cursor: pointer;
        border-right: 1px solid var(--border-color);
    }

        .view-toggles .view-btn:last-child {
            border-right: none;
        }

        .view-toggles .view-btn.active {
            background-color: var(--primary-color);
            color: white;
        }


.detail-table {
    width: 100%;
    border-collapse: collapse;
}

    .detail-table th, .detail-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
        font-size: 0.9rem;
    }

    .detail-table th {
        font-weight: bold;
        color: var(--text-light);
        width: 200px; 
    }


.detail-card {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color);
}

    .detail-card h4 {
        margin-top: 0;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 10px;
        margin-bottom: 15px;
    }

    .detail-card .data-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .detail-card .data-item {
        font-size: 0.9rem;
    }

    .detail-card .data-label {
        display: block;
        color: var(--text-light);
        font-size: 0.8rem;
    }

    .detail-card .data-value {
        font-weight: bold;
        color: var(--text-dark);
    }