﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace WebApplication2.Controllers
{
    public class PartsController : Controller
    {
        string constr = ConfigurationManager.ConnectionStrings["FSMGold"].ConnectionString;
        // GET: Parts
        public ActionResult Parts1()
        {
            return View();
        }
        public JsonResult GetParts1()
        {
            // 1. Create a list to hold the rows from the database
            var data = new List<Dictionary<string, object>>();

            // 2. Get the connection string from Web.config
           
            // 3. Use 'using' to ensure the connection is closed automatically
            using (SqlConnection con = new SqlConnection(constr))
            {
                using (SqlCommand cmd = new SqlCommand("gnmmodeldata", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    con.Open();

                    using (SqlDataReader rdr = cmd.ExecuteReader())
                    {
                        // 4. Loop through each row
                        while (rdr.Read())
                        {
                            var row = new Dictionary<string, object>();
                            // 5. Loop through each column in the row
                            for (int i = 0; i < rdr.FieldCount; i++)
                            {
                                row[rdr.GetName(i)] = rdr.GetValue(i);
                            }
                            data.Add(row);
                        }
                    }
                }
                con.Close();
            }

            // 6. Return the list as a JSON object
            // The JsonRequestBehavior.AllowGet is required for security reasons
            return Json(data, JsonRequestBehavior.AllowGet);
        }
        [HttpPost]
        public JsonResult Getdata(int functionGroupId, int companyId)
        {
            try
            {
                var data = new List<Dictionary<string, object>>();
                

                using (SqlConnection con = new SqlConnection(constr))
                {
                    string query = "SELECT * FROM GNM_FunctionGroup WHERE FunctionGroup_ID = @FuncId AND Company_ID = @CompId";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@FuncId", functionGroupId);
                        cmd.Parameters.AddWithValue("@CompId", companyId);

                        con.Open();
                        using (SqlDataReader rdr = cmd.ExecuteReader())
                        {
                            while (rdr.Read())
                            {
                                var row = new Dictionary<string, object>();
                                for (int i = 0; i < rdr.FieldCount; i++)
                                {
                                    var value = rdr.GetValue(i);

                                    // Check if the value is a DateTime object
                                    if (value is DateTime)
                                    {
                                        // Format it into a string and add it to the row
                                        row[rdr.GetName(i)] = ((DateTime)value).ToString("dd MMMM yyyy HH:mm:ss");
                                    }
                                    else
                                    {
                                        // Otherwise, add the value as-is
                                        row[rdr.GetName(i)] = value;
                                    }
                                }
                                data.Add(row);
                            }
                        } 
                        con.Close();
                    }
                }
                return Json(data);
            }
            catch (Exception ex)
            {
                // If an error occurs, send back a 500 status code
                // and the specific error message as JSON.
                Response.StatusCode = 500;
                return Json(new { error = ex.Message });
            }
        }
        public JsonResult GetParts()
        {
            // 1. Create a list to hold the rows from the database
            List<PartsDetails> parts = new List<PartsDetails>();
            PartsDetails partsList = new PartsDetails();

            // 2. Get the connection string from Web.config
            string constr = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;

            // 3. Use 'using' to ensure the connection is closed automatically
            using (SqlConnection con = new SqlConnection(constr))
            {
                using (SqlCommand cmd = new SqlCommand("gnmmodeldata", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    con.Open();

                    using (SqlDataReader rdr = cmd.ExecuteReader())
                    {
                        // 4. Loop through each row
                        while (rdr.Read())
                        {
                            partsList.Model_ID = (rdr["Model_ID"]==DBNull.Value)?0:Convert.ToInt32(rdr["Model_ID"]);
                            partsList.ProductType_ID = (rdr["ProductType_ID"] == DBNull.Value) ? 0 : Convert.ToInt32(rdr["ProductType_ID"]);
                            partsList.Brand_ID = (rdr["Brand_ID"] == DBNull.Value) ? 0 : Convert.ToInt32(rdr["Brand_ID"]);
                            partsList.Model_Name = rdr["Model_Name"].ToString();

                            parts.Add(partsList);
                        }
                    }
                }
                con.Close();
            }

            return Json(parts, JsonRequestBehavior.AllowGet);
        }

    }
    public class PartsDetails
    {
        public int Model_ID { get; set; }
        public int ProductType_ID { get; set; }
        public decimal Brand_ID { get; set; }
        public string Model_Name { get; set; }

    }

}



    