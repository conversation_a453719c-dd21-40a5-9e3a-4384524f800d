﻿/* --- Variables and Basic Setup --- */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --border-color: #dee2e6;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --text-dark: #212529;
    --text-light: #6c757d;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

body {
    margin: 0;
    font-family: 'Inter', system-ui, sans-serif;
    background-color: var(--bg-light);
    color: var(--text-dark);
}

.main-container {
/*    max-width: 1400px;*/
    margin: 20px auto;
    padding: 25px;
}

h1, h2 {
    color: var(--text-dark);
}

/* --- Header & Controls --- */
.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.controls {
    margin-bottom: 1rem;
}

.view-btn {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    background: var(--bg-white);
    color: var(--text-light);
    cursor: pointer;
    border-radius: 6px;
    margin-right: 5px;
    font-weight: 500;
}

    .view-btn.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

.action-btn {
    padding: 10px 18px;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

/* --- Main Data Display Area --- */
.grid-header {
    display: none; /* Hidden by default, shown by specific view classes */
    grid-template-columns: 2.5fr 1.5fr 1fr 1.5fr 1fr;
    align-items: center;
    padding: 12px 15px;
    font-weight: 600;
    color: var(--text-light);
    font-size: 0.85em;
    text-transform: uppercase;
    border-bottom: 1px solid var(--border-color);
}

.data-item {
    background: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: box-shadow 0.2s ease-in-out;
}

    .data-item:hover {
        box-shadow: var(--shadow);
    }

.item-header {
    font-weight: 600;
    color: var(--text-dark);
}

.item-field {
    font-size: 0.9em;
    color: var(--text-light);
}

    .item-field strong {
        color: var(--text-dark);
    }

.item-actions {
    justify-self: end;
}

/* --- VIEW STYLES --- */

/* Grid View (Table-like) */
#data-container.grid-view .grid-header {
    display: grid;
}

#data-container.grid-view .data-item {
    display: grid;
    grid-template-columns: 2.5fr 1.5fr 1fr 1.5fr 1fr;
    align-items: center;
    padding: 15px;
    border-radius: 0;
    border-top: none;
    box-shadow: none;
}

    #data-container.grid-view .data-item:first-child {
        border-top: 1px solid var(--border-color);
    }

/* Card View */
#data-container.card-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
}

    #data-container.card-view .data-item {
        padding: 20px;
    }

    #data-container.card-view .item-header {
        margin-bottom: 12px;
    }

    #data-container.card-view .item-field {
        margin: 4px 0;
    }

    #data-container.card-view .item-actions {
        position: absolute;
        top: 15px;
        right: 15px;
    }

/* List View */
#data-container.list-view {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

    #data-container.list-view .data-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
    }

    #data-container.list-view .item-content {
        display: flex;
        gap: 25px;
        align-items: center;
    }

/* --- Paging Controls --- */
.paging-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 25px;
    gap: 8px;
}

.paging-btn {
    padding: 8px 14px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-white);
    border-radius: 6px;
    cursor: pointer;
}

    .paging-btn.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

/* --- Modal & Dynamic Form Styles --- */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.6);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 11px;
    border: none;
    width: 90%;
/*    max-width: 900px;*/
    border-radius: 8px;
/*    box-shadow: 0 5px 15px rgba(0,0,0,0.3);*/
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

#readingForm {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, auto));
    gap: 20px;
}
}

.details-container {
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-top: 20px;
    background-color: var(--bg-light);
}

    .details-container h4 {
        margin-top: 0;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

.reading-item {
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 15px;
    background-color: var(--bg-white);
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

    .form-group label {
        margin-bottom: 5px;
        font-size: 0.9em;
        color: var(--text-light);
        font-weight: bold;
    }

    .form-group input[type="text"],
    .form-group input[type="number"],
    .form-group input[type="datetime-local"] {
        padding: 10px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        width: 84%;
        box-sizing: border-box; /* Important for padding and width */
    }

.warranty-status {
    font-weight: bold;
    padding: 8px;
    border-radius: 4px;
    text-align: center;
}


/*REFREENCEEE*/

/* ADD THIS TO YOUR STYLES.CSS FILE */

/* Card View */
#data-container.card-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

    #data-container.card-view .data-item {
        padding: 0; /* Remove padding for custom card layout */
        box-shadow: var(--shadow);
        display: flex;
        flex-direction: column;
    }

    #data-container.card-view .item-content {
        padding: 20px;
    }

    #data-container.card-view .item-actions {
        margin-top: auto; /* Pushes actions to the bottom */
        padding: 15px 20px;
        background-color: var(--bg-light);
        width: 100%;
        box-sizing: border-box;
        display: flex;
        gap: 10px;
        border-top: 1px solid var(--border-color);
    }

/* List View */
#data-container.list-view .data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

#data-container.list-view .item-content {
    display: flex;
    gap: 25px;
    align-items: center;
}

/* Compact View */
#data-container.compact-view .data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--bg-light);
}

#data-container.compact-view .item-content {
    display: flex;
    gap: 20px;
    font-size: 0.9em;
}

/* ADD THIS TO THE BOTTOM OF YOUR STYLES.CSS FILE */

/* --- New Card View Styles --- */
.vehicle-card {
    background: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column; /* Stacks header, body, and footer vertically */
    border-top: 4px solid var(--primary-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title h3 {
    margin: 0 0 5px 0;
    color: var(--text-dark);
}

.card-title span {
    font-size: 0.9em;
    color: var(--text-light);
}

.card-status {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.8em;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.card-body {
    padding: 1.25rem;
    flex-grow: 1; /* Allows body to take up available space */
}

    .card-body .detail-item {
        margin-bottom: 10px;
        color: var(--text-light);
    }

        .card-body .detail-item strong {
            color: var(--text-dark);
        }

.card-footer {
    display: flex;
    gap: 10px;
    padding: 1rem 1.25rem;
    background-color: var(--bg-light);
    border-top: 1px solid var(--border-color);
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

    .card-footer button {
        flex: 1; /* Makes buttons share space equally */
        padding: 10px;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
    }

.action-btn-secondary {
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
    color: var(--text-dark);
}

.action-btn-danger {
    background-color: #fee2e2;
    border: 1px solid #fecaca;
    color: #991b1b;
}

/* --- Status Badge Colors (Examples) --- */
.status, .card-status {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.8em;
    font-weight: 600;
}

.status-in-service {
    background-color: #d1fae5;
    color: #067647;
}

.status-rental {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-scrap {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-unknown {
    background-color: #f3f4f6;
    color: #4b5563;
}

.details-container.vehicle-details-box {
    /* This turns the container into a grid */
    display: grid;
    
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    /* This adds spacing between the form fields */
    gap: 15px 20px;
}

/* This makes sure the title spans the full width of the grid */
.details-container h4 {
    grid-column: 1 / -1;
}

/* This ensures the checkbox groups look clean */
.checkbox-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    padding-top: 20px; /* Aligns them better with other fields */
}
/* Add this CSS to style the new readings table */
.readings-table {
    width: 90%;
    border-collapse: collapse;
    margin-top: 10px;
}

    .readings-table th, .readings-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .readings-table th {
        font-size: 0.85em;
        color: var(--text-light);
        text-transform: uppercase;
    }

    .readings-table td input {
        width: 100%;
        box-sizing: border-box;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    .readings-table tr:last-child td {
        border-bottom: none;
    }

    .readings-table td input:disabled {
        background-color: #f8f9fa;
        border-color: #e9ecef;
        color: #6c757d;
    }