$(function () {
    let allUsers = []; // Master list of users
    let currentView = 'table'; // Default view

    // --- RENDER FUNCTIONS ---
    // --- RENDER FUNCTIONS ---
    function renderTable(users) {
        let container = $('#dataContainer');
        container.empty();
        if (users.length === 0) { container.html("No users found."); return; }

        let table = $('<table class="styled-table"></table>');
        table.append('<thead><tr><th>First Name</th><th>Last Name</th><th>Email</th><th>Date Created</th><th style="width: 150px;">Actions</th></tr></thead>');

        let tbody = $('<tbody></tbody>');
        users.forEach(user => {
            let date = new Date(parseInt(user.DateCreated.substr(6))).toLocaleDateString();
            let row = `<tr data-user-id="${user.UserID}" data-first-name="${user.FirstName}" data-last-name="${user.LastName}" data-email="${user.Email}">
                         <td>${user.FirstName}</td><td>${user.LastName}</td><td>${user.Email}</td><td>${date}</td>
                         <td><button class="btn btn-sm btn-info edit-btn">Edit</button> <button class="btn btn-sm btn-danger delete-btn">Delete</button></td>
                       </tr>`;
            tbody.append(row);
        });
        table.append(tbody);
        container.append(table);
    }

    function renderCardView(users) {
        let container = $('#dataContainer');
        container.empty();
        if (users.length === 0) { container.html("No users found."); return; }

        let cardContainer = $('<div class="row"></div>');
        users.forEach(user => {
            let date = new Date(parseInt(user.DateCreated.substr(6))).toLocaleDateString();
            let card = `
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">${user.FirstName} ${user.LastName}</h5>
                            <p class="card-text">${user.Email}</p>
                            <p class="card-text"><small class="text-muted">Created: ${date}</small></p>
                            <button class="btn btn-sm btn-info edit-btn" data-user-id="${user.UserID}" data-first-name="${user.FirstName}" data-last-name="${user.LastName}" data-email="${user.Email}">Edit</button>
                            <button class="btn btn-sm btn-danger delete-btn" data-user-id="${user.UserID}">Delete</button>
                        </div>
                    </div>
                </div>`;
            cardContainer.append(card);
        });
        container.append(cardContainer);
    }

    // --- MAIN RENDER CONTROLLER ---
    function applyFiltersAndRender() {
        let filteredUsers = allUsers;

        // Apply Search Filter
        const searchTerm = $('#searchInput').val().toLowerCase();
        if (searchTerm) {
            filteredUsers = filteredUsers.filter(user =>
                (user.FirstName && user.FirstName.toLowerCase().includes(searchTerm)) ||
                (user.LastName && user.LastName.toLowerCase().includes(searchTerm)) ||
                (user.Email && user.Email.toLowerCase().includes(searchTerm))
              
            );
        }

        // Render the correct view
        if (currentView === 'table') {
            renderTable(filteredUsers);
        } else {
            renderCardView(filteredUsers);
        }
    }

    // --- DATA LOADING ---
    function loadUsers(userId = '') {
        $.ajax({
            url: "/All/GetData",
            type: "GET",
            data: { userId: userId },
            success: function (users) {
                allUsers = users;
                applyFiltersAndRender();
            }
        });
    }

    function loadUserIdFilter() {
        $.get("/All/GetUserIds", function (ids) {
            const dropdown = $('#userIdFilter');
            ids.forEach(id => dropdown.append(`<option value="${id}">${id}</option>`));
        });
    }

    // --- EVENT HANDLERS ---
    $('#createUserForm').on('submit', function (e) {
        e.preventDefault();
        $.ajax({
            url: "/All/CreateUser",
            type: "POST",
            data: $(this).serialize(),
            success: function (response) {
                $('#statusMessage').text(response.message).css("color", "green");
                $('#createUserForm')[0].reset();
                loadUsers();
            }
        });
    });

    $('#dataContainer').on('click', '.edit-btn', function () {
        // First, find the parent table row (tr) of the button that was clicked
        const row = $(this).closest('tr');

        // Now, read the data from the 'row' element
        $('#editUserId').val(row.data('user-id'));
        $('#editFirstName').val(row.data('first-name'));
        $('#editLastName').val(row.data('last-name'));
        $('#editEmail').val(row.data('email'));

        // Show the modal
        $('#editModal').modal('show');
    });

    // --- UPDATE ---
    // When "Save Changes" is clicked in the modal
    $('#editForm').on('submit', function (e) {
        e.preventDefault();
        $.ajax({
            url: "/All/UpdateUser", // Correct URL
            type: "POST",
            data: $(this).serialize(), // serialize() correctly packages form data
            success: function (response) {
                $('#statusMessage').text(response.message).css("color", "blue");
                $('#editModal').modal('hide');
                loadUsers(); // Refresh the table
            },
            error: function () {
                alert("Error: Could not update user.");
            }
        });
    });

    // --- DELETE ---
    $('#dataContainer').on('click', '.delete-btn', function () {
        const userId = $(this).closest('tr').data('user-id');
        if (confirm(`Are you sure you want to delete user #${userId}?`)) {
            $.ajax({
                url: "/All/DeleteUser", // Correct URL
                type: "POST",
                data: { UserID: userId }, // Data object key "UserID" matches C# parameter
                success: function (response) {
                    $('#statusMessage').text(response.message).css("color", "red");
                    loadUsers(); // Refresh the table
                },
                error: function () {
                    alert("Error: Could not delete user.");
                }
            });
        }
    });

    $('#searchInput').on('keyup', applyFiltersAndRender);

    $('#userIdFilter').on('change', function () {
        loadUsers($(this).val());
    });

    $('.view-toggle-btn').on('click', function () {
        $('.view-toggle-btn').removeClass('active');
        $(this).addClass('active');
        currentView = $(this).data('view');
        <small>Created: ${new Date(parseInt(v.DateCreated.substr(6))).toLocaleDateString()}</small>
        applyFiltersAndRender();
    });

    // --- INITIAL LOAD ---
    loadUsers();
    loadUserIdFilter();
});