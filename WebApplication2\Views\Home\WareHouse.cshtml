﻿
@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>WareHouse</title>
            <meta charset="UTF-8">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
            <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
            @*<link href="~/CoreWareHouseView/CSS/font-awesome.min.css" rel="stylesheet" />
            <link href="~/CoreWareHouseView/Fonts/fontawesome-webfont.eot" rel="stylesheet">
            <link href="~/CoreWareHouseView/Fonts/fontawesome-webfont.ttf" rel="stylesheet">
            <link href="~/CoreWareHouseView/Fonts/fontawesome-webfont.woff" rel="stylesheet">*@
            <link href="~/Assets/css/styles.css" rel="stylesheet" />
        </head>

<body>
    <div>
        <div class="CW-container">

            <div class="CW-top-header-bar">
                @*<div class="CW-stats-container">
                        <div class="CW-stat-card">
                            <i class="fas fa-check-circle CW-stat-icon CW-completed"></i>
                            <div>
                                <span class="CW-stat-value">150</span>
                                <span class="CW-stat-label">Completed</span>
                            </div>
                        </div>
                        <div class="CW-stat-card">
                            <i class="fas fa-hourglass-half CW-stat-icon CW-pending"></i>
                            <div>
                                <span class="CW-stat-value">25</span>
                                <span class="CW-stat-label">Pending</span>
                            </div>
                        </div>
                        <div class="CW-stat-card">
                            <i class="fas fa-times-circle CW-stat-icon CW-rejected"></i>
                            <div>
                                <span class="CW-stat-value">5</span>
                                <span class="CW-stat-label">Rejected</span>
                            </div>
                        </div>
                    </div>*@
                <div class="CW-top-right-controls">
                    <div class="CW-toolbar-item">
                        <strong>Branch:</strong>
                        <span id="branchName">Bay Area</span>
                    </div>
                </div>
            </div>

            <div class="CW-bottom-header-bar CW-controls-bar">
                <div class="CW-bottom-left-controls CW-left-controls">
                    <div class="CW-view-controls">
                        <button class="CW-view-btn CW-active" data-view="grid"><i class="fas fa-th-large"></i> Grid View</button>
                        <button class="CW-view-btn" data-view="card"><i class="fas fa-grip-horizontal"></i> Card View</button>
                        <button class="CW-view-btn" data-view="compact"><i class="fas fa-th"></i> Compact View</button>
                        <button class="CW-view-btn" data-view="list"><i class="fas fa-list"></i> List View</button>
                    </div>
                </div>
                <div class="CW-bottom-right-controls CW-right-controls">
                    <div class="CW-search-container">
                        <i class="fas fa-search CW-search-icon"></i>
                        <input type="text" class="CW-search-input" placeholder="Search...">
                    </div>
                    <button id="addBtn" class="CW-btn CW-btn-success"><i class="fas fa-plus"></i> Add New</button>
                    <button id="exportBtn" class="CW-btn CW-btn-primary"><i class="fas fa-file-export"></i> Export</button>
                    <button id="refreshBtn" class="CW-btn CW-btn-primary"><i class="fas fa-sync-alt"></i></button>
                </div>
            </div>

            <div id="statusMessage" style="margin-top: 10px; text-align: center;"></div>

            <div class="CW-data-container">
                <div class="CW-view-content CW-active" id="grid-view">
                    <div class="CW-table-container">
                        <table class="CW-data-table">
                            <thead>
                                <tr>
                                    <th class="CW-checkbox-col"><input type="checkbox"></th>
                                    <th>Name</th>
                                    <th>Is Default?</th>
                                    <th>Is Active?</th>
                                    <th>Is Visible?</th>
                                    <th class="CW-actions-col">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="data-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="CW-pagination-container">
                <div class="CW-pagination-info">
                    <span>Rows per page:</span>
                    <select>
                        <option>100</option>
                    </select>
                    <span>View 0 - 0 of 0</span>
                </div>
                <div class="CW-pagination-controls">
                    <button class="CW-pagination-btn" disabled><i class="fas fa-angle-double-left"></i></button>
                    <button class="CW-pagination-btn" disabled><i class="fas fa-angle-left"></i></button>
                    <div class="CW-page-numbers">
                        <button class="CW-page-number CW-active">1</button>
                    </div>
                    <button class="CW-pagination-btn" disabled><i class="fas fa-angle-right"></i></button>
                    <button class="CW-pagination-btn" disabled><i class="fas fa-angle-double-right"></i></button>
                </div>
            </div>

        </div>
    </div>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <script src="~/Assets/js/CWscripts.js"></script>
</body>
</html>
