﻿@{
    ViewBag.Title = "Partssss";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leads Management</title>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

    <link href="~/Assets/css/styles.css" rel="stylesheet" />
</head>
<body>
    <div class="container mt-4">
        <h2>Leads Management</h2>
        <hr>
        <div class="row mb-3">
            <div class="col-md-4">
                <label for="employeeFilter">Filter by Assigned Employee ID:</label>
                <select id="employeeFilter" class="form-control"></select>
            </div>
            <div class="col-md-8 text-right">
                <button id="addLeadBtn" class="btn btn-primary">Add New Lead</button>
            </div>
        </div>
        <div id="tableContainer"></div>

        <div class="modal fade" id="leadModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <form id="leadForm">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modalTitle">Add Lead</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <input type="hidden" id="leadId" name="LeadID">
                            <div class="form-group">
                                <label>First Name</label>
                                <input type="text" class="form-control" id="firstName" name="FirstName" required>
                            </div>
                            <div class="form-group">
                                <label>Last Name</label>
                                <input type="text" class="form-control" id="lastName" name="LastName" required>
                            </div>
                            <div class="form-group">
                                <label>Email</label>
                                <input type="email" class="form-control" id="email" name="Email" required>
                            </div>
                            <div class="form-group">
                                <label>Company</label>
                                <input type="text" class="form-control" id="company" name="Company">
                            </div>
                            <div class="form-group">
                                <label>Status</label>
                                <input type="text" class="form-control" id="status" name="Status">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary">Save changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script src="~/Assets/js/test.js"></script>
</body>
</html>