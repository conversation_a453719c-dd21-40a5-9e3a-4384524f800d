﻿
@{
    Layout = "~/Views/Shared/_Layout.cshtml";

}

<!DOCTYPE html>
<html>
<head>
    <title>Show Text on Button Click</title>
    <link href="~/Assets/css/styles.css" rel="stylesheet" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
</head>
<body>

    <button onclick="showText()">Click Me!</button><br />
    <input type="text" id="functionGroupId" placeholder="Enter FunctionGroup ID" />
    <label>@HttpContext.GetGlobalResourceObject("sample", "FunctionGroup_ID")</label><br />

    <input type="text" id="companyId" placeholder="Enter Company ID" />
    <label>@HttpContext.GetGlobalResourceObject("sample","Company_ID")</label><br />

    <button id="submitBtn">SUBMIT</button>
    <hr> <div id="resultsContainer"></div>
    <p id="hiddenText">Hello! This text is now visible.</p>

    <script>
        function showText() {
            debugger
            $.ajax({
                url: "Parts/GetParts",
                success: function (result) {
                    $("#hiddenText").html(result).show();
                    debugger
                    console.log(result)
                },
                Error: function () {
                    alert("error");
                }
            });
        }
        $(function () {
            $("#submitBtn").on("click", function () {
                let funcId = $("#functionGroupId").val();
                let compId = $("#companyId").val();

                if (!funcId || !compId) {
                    alert("Please enter both IDs.");
                    return;
                }

                $.ajax({
                    url: "/Parts/Getdata",
                    type: "POST",
                    data: {
                        functionGroupId: funcId,
                        companyId: compId
                    },
                    dataType: "json",
                    success: function (result) {
                        let container = $('#resultsContainer');
                        container.empty(); // Clear previous results

                        // Check if any data was returned
                        if (result.length === 0) {
                            container.html("No data found for the specified IDs.");
                            return;
                        }

                        // 1. Create the table element
                        let table = $('<table class="table table-bordered"></table>');

                        // 2. Create the table header
                        let thead = $('<thead></thead>');
                        let headerRow = $('<tr></tr>');
                        // Use the keys from the first result object for header columns
                        Object.keys(result[0]).forEach(function (key) {
                            headerRow.append($('<th></th>').text(key));
                        });
                        thead.append(headerRow);
                        table.append(thead);

                        // 3. Create the table body and fill it with data
                        let tbody = $('<tbody></tbody>');
                        result.forEach(function (rowData) {
                            let row = $('<tr></tr>');
                            Object.values(rowData).forEach(function (value) {
                                row.append($('<td></td>').text(value));
                            });
                            tbody.append(row);
                        });
                        table.append(tbody);

                        // 4. Add the completed table to the container div
                        container.append(table);
                    },
                    error: function (xhr, status, error) {
                        console.error("AJAX Error:", status, error);
                        alert("An error occurred while fetching the data.");
                    }
                });
            });
        });

    </script>

</body>
</html>
