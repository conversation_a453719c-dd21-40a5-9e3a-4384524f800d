﻿@{ Layout = null; }
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vehicle</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
     <link href="~/Assets/css/Vehicle.css" rel="stylesheet" />
</head>
<body>
    <div class="main-container">
        <div class="list-panel">
            <div class="list-header">
                <div class="select-all-container">
                    <input type="checkbox" id="selectAllCheckbox">
                    <label for="selectAllCheckbox">@HttpContext.GetGlobalResourceObject("sample","Vehicle_List")</label>
                </div>
                @*<button id="addVehicleBtn" class="add-btn" title="Add New Vehicle">+</button>*@
            <button id="deleteSelectedBtn" class="action-btn" disabled>@HttpContext.GetGlobalResourceObject("sample", "Delete_Selected")</button>
            </div>
            <div id="vehicleList" class="list-body"></div>
            <div id="paginationContainer" class="list-footer"></div>
        </div>

        <div class="detail-panel">
            <div class="detail-header">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="Search...">
                </div>
                <div>
                    <div class="view-toggles">
                        <button class="view-btn active" data-view="list">List View</button>
                        <button class="view-btn" data-view="table">Table View</button>
                        <button class="view-btn" data-view="card">Card View</button>
                    
                    <button id="addVehicleBtn" class="action-btn">@HttpContext.GetGlobalResourceObject("sample", "Add_new")</button>
                    <button id="editBtn" class="action-btn" disabled>@HttpContext.GetGlobalResourceObject("sample", "Edit_data")</button>
                    <button id="deleteBtn" class="action-btn" disabled>@HttpContext.GetGlobalResourceObject("sample", "Delete")</button>
                        </div>
                </div>
            </div>
            <div id="warrantyStatusContainer" class="alert alert-info Warranty-text">
            </div>
            <div id="detailViewContainer" class="detail-body">
                <p class="placeholder-text">Select a vehicle from the list to see details.</p>
            </div>
        </div>
    </div>

    <div id="toastContainer"></div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="~/Assets/js/NewVehicle.js"></script>
</body>
</html>