﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

public class Vehicle
{
    [Display(Name = "Serial Number")]
    public string SerialNumber { get; set; }
    public string Brand { get; set; }
    public string Model { get; set; }
    [Display(Name = "Service Engineer")]
    public string ServiceEngineer { get; set; }
    [Display(Name = "Primary Segment")]
    public string PrimarySegment { get; set; }
    [Display(Name = "Sale Date")]
    public DateTimeOffset SaleDate { get; set; }
    [Display(Name = "Average HMR")]
    public decimal AverageHMR { get; set; }
    [Display(Name = "Sales Order Number")]
    public string SaleszorderNumber { get; set; }
    [Display(Name = "Next Service Type")]
    public string Nextservicetype { get; set; }
    [Display(Name = "Is Active?")]
    public bool Isactive { get; set; }
    [Display(Name = "Last HMR Updated Date")]
    public DateTimeOffset LastHMRupdateddate { get; set; }
    [Display(Name = "Is Imported?")]
    public bool Isimported { get; set; }
    public string Remarks { get; set; }
    [Display(Name = "Operator Name")]
    public string Operatorname { get; set; }
    [Display(Name = "Product Type")]
    public string Producttype { get; set; }
    [Display(Name = "Machine Status")]
    public string Machinestatus { get; set; }
    [Display(Name = "Secondary Segment")]
    public string Secondarysegment { get; set; }
    [Display(Name = "Commissioning Date")]
    public DateTimeOffset Commissioningdate { get; set; }
    [Display(Name = "Machine HMR")]
    public short MachineHMR { get; set; }
    [Display(Name = "Last Service Date")]
    public DateTimeOffset Lastservicedate { get; set; }
    [Display(Name = "Current Site Address")]
    public string Cuurentsiteaddress { get; set; }
    [Display(Name = "Next Service Date")]
    public DateTimeOffset nextservicedate { get; set; }
    public string WarrantyStatus { get; set; }

}
public class ProductReading
{
    public string Company { get; set; }
    public string Mode { get; set; }
    public string ReferenceNumber { get; set; }
    public string SerialNumber { get; set; }
    public DateTimeOffset ReferenceDate { get; set; }
}

public class FullDetailViewModel
{
    public string Company { get; set; }
    public string Mode_Reading { get; set; }
    public string ReferenceNumber { get; set; }
    public DateTimeOffset? ReferenceDate { get; set; }
    public string SerialNumber { get; set; }
    public string Brand { get; set; }
    public string Model_Vehicle { get; set; }
    public string ServiceEngineer { get; set; }
    public string PrimarySegment { get; set; }
    public DateTimeOffset? SaleDate { get; set; }
    public decimal? AverageHMR { get; set; }
    public string SaleszorderNumber { get; set; }
    public string Nextservicetype { get; set; }
    public bool? Isactive { get; set; }
    public DateTimeOffset? LastHMRupdateddate { get; set; }
    public bool? Isimported { get; set; }
    public string Remarks { get; set; }
    public string Operatorname { get; set; }
    public string Producttype { get; set; }
    public string Machinestatus { get; set; }
    public string Secondarysegment { get; set; }
    public DateTimeOffset? Commissioningdate { get; set; }
    public short? MachineHMR { get; set; }
    public DateTimeOffset? Lastservicedate { get; set; }
    public string Cuurentsiteaddress { get; set; }
    public DateTimeOffset? nextservicedate { get; set; }
    public string VehicleWarranty { get; set; }
}

// CORRECTED: Duplicate 'MachineStatus' property removed
public class ProductReadingViewModel
{
    public string SerialNumber { get; set; }
    public string VehicleModel { get; set; }
    public string Brand { get; set; }
    public string Machinestatus { get; set; }
    public string PrimarySegment { get; set; }
    public DateTimeOffset? Lastservicedate { get; set; }
    public DateTimeOffset? nextservicedate { get; set; }
    public DateTimeOffset? SaleDate { get; set; }
    public string ReferenceNumber { get; set; }
    public string Company { get; set; }
    public DateTimeOffset? ReferenceDate { get; set; }
    public string VehicleWarranty { get; set; }
}


public class VehicleDetailWithReadingsViewModel
{
    public FullDetailViewModel VehicleInfo { get; set; }
    public List<ProductReading> Readings { get; set; }
}

public class PagedResultViewModel
{
    public List<ProductReadingViewModel> Items { get; set; }
    public int TotalCount { get; set; }
}