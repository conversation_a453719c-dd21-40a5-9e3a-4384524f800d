﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace WebApplication2.Controllers
{
    public class TestController : Controller
    {
        // GET: Test
        public ActionResult Test()
        {
            return View();
        }
        [HttpGet]
        public JsonResult GetData()
        {
            var data = new List<Dictionary<string, object>>();
            // 1. Use your new connection string name
            string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;

            using (SqlConnection con = new SqlConnection(constr))
            {
                // 2. Use your actual table name
                string query = "SELECT * FROM Users";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    con.Open();
                    using (SqlDataReader rdr = cmd.ExecuteReader())
                    {
                        while (rdr.Read())
                        {
                            var row = new Dictionary<string, object>();
                            for (int i = 0; i < rdr.FieldCount; i++)
                            {
                                // Get the value from the reader
                                var value = rdr.GetValue(i);

                                // ✨ CHANGE: Check if the value is a DateTime and format it
                                if (value is DateTime)
                                {
                                    // You can change the format string "yyyy-MM-dd" to whatever you need
                                    row[rdr.GetName(i)] = ((DateTime)value).ToString("yyyy-MM-dd HH:mm:ss");
                                }
                                else
                                {
                                    // Also handles DBNull by converting it to null for proper JSON
                                    row[rdr.GetName(i)] = (value == DBNull.Value) ? null : value;
                                }
                            }
                            data.Add(row);
                        }
                    }
                }
            }
            return Json(data, JsonRequestBehavior.AllowGet);
        }
        [HttpPost]
        public JsonResult CreateUser(string FirstName, string LastName, string Email, string Password)
        {
            try
            {
                // IMPORTANT: Never store plain text passwords. Always hash them.
                // This is a simple example. For production, use a strong library like BCrypt.Net.
                string passwordHash = YourPasswordHashingFunction(Password);

                string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(constr))
                {
                    // Use an INSERT statement with your column names.
                    // Do not insert UserID; the database should generate it.
                    string query = @"INSERT INTO Users (FirstName, LastName, Email, PasswordHash, DateCreated) 
                             VALUES (@FirstName, @LastName, @Email, @PasswordHash, @DateCreated)";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        // Use parameters to prevent SQL Injection
                        cmd.Parameters.AddWithValue("@FirstName", FirstName);
                        cmd.Parameters.AddWithValue("@LastName", LastName);
                        cmd.Parameters.AddWithValue("@Email", Email);
                        cmd.Parameters.AddWithValue("@PasswordHash", passwordHash);
                        cmd.Parameters.AddWithValue("@DateCreated", DateTime.Now);

                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
                return Json(new { success = true, message = "User created successfully!" });
            }
            catch (Exception ex)
            {
                Response.StatusCode = 500;
                return Json(new { success = false, message = ex.Message });
            }
        }

        // Example placeholder for a hashing function
        private string YourPasswordHashingFunction(string password)
        {
          
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }
        [HttpPost]
        public JsonResult UpdateUser(int UserID, string FirstName, string LastName, string Email)
        {
            try
            {
                string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(constr))
                {
                    // 1. Create a SQL UPDATE statement with a WHERE clause
                    string query = @"UPDATE Users 
                             SET FirstName = @FirstName, 
                                 LastName = @LastName, 
                                 Email = @Email 
                             WHERE UserID = @UserID";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        // 2. Add parameters to prevent SQL Injection
                        cmd.Parameters.AddWithValue("@FirstName", FirstName);
                        cmd.Parameters.AddWithValue("@LastName", LastName);
                        cmd.Parameters.AddWithValue("@Email", Email);
                        cmd.Parameters.AddWithValue("@UserID", UserID); // For the WHERE clause

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery(); // Use for UPDATEs

                        if (rowsAffected > 0)
                        {
                            return Json(new { success = true, message = "User updated successfully!" });
                        }
                        else
                        {
                            return Json(new { success = false, message = "User not found." });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Response.StatusCode = 500;
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        public JsonResult DeleteUser(int UserID)
        {
            try
            {
                string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;
                using (SqlConnection con = new SqlConnection(constr))
                {
                    // IMPORTANT: Always use a WHERE clause for DELETE statements
                    string query = "DELETE FROM Users WHERE UserID = @UserID";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@UserID", UserID);

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            return Json(new { success = true, message = "User deleted successfully!" });
                        }
                        else
                        {
                            return Json(new { success = false, message = "User not found." });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Response.StatusCode = 500;
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
    }
