﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing.Printing;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using WebApplication2.Models;

namespace WebApplication2.Controllers
{
    public class VehicleProductController : Controller
    {
        // GET: VehicleProduct
        public ActionResult NEWW()
        {
            return View();
        }
        private string GetConnectionString()
        {
            return ConfigurationManager.ConnectionStrings["ALIEN"].ConnectionString;
        }

        
        private T GetValue<T>(SqlDataReader rdr, string columnName)
        {
            object value = rdr[columnName];
            if (value == DBNull.Value)
            {
                return default(T); 
            }
            return (T)value;
        }


        [HttpGet]
        public JsonResult GetAll(int pageNumber = 1, int pageSize = 15)
        {
            var pagedResult = new PagedResultViewModel { Items = new List<ProductReadingViewModel>() };
            try
            {
                using (var con = new SqlConnection(GetConnectionString()))
                {
                    using (var cmd = new SqlCommand("USP_READinitialdata", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PageNumber", pageNumber);
                        cmd.Parameters.AddWithValue("@PageSize", pageSize);
                        con.Open();
                        using (var rdr = cmd.ExecuteReader())
                        {
                            while (rdr.Read()) {
                                pagedResult.Items.Add(new ProductReadingViewModel {
                                    SerialNumber = GetValue<string>(rdr, "SerialNumber"),
                                    VehicleModel = GetValue<string>(rdr, "VehicleModel"),
                                    Brand = GetValue<string>(rdr, "Brand"),
                                    Machinestatus = GetValue<string>(rdr, "Machinestatus"),
                                    PrimarySegment = GetValue<string>(rdr, "PrimarySegment"),
                                    Lastservicedate = GetValue<DateTimeOffset?>(rdr, "Lastservicedate"),
                                    nextservicedate = GetValue<DateTimeOffset?>(rdr, "nextservicedate"),
                                    SaleDate = GetValue<DateTimeOffset?>(rdr, "SaleDate"),
                                    ReferenceNumber = GetValue<string>(rdr, "ReferenceNumber"),
                                    Company = GetValue<string>(rdr, "Company"),
                                    ReferenceDate = GetValue<DateTimeOffset?>(rdr, "ReferenceDate"),
                                    VehicleWarranty = GetValue<string>(rdr, "VehicleWarranty")
                                   
                                }); 
                            }
                            if (rdr.NextResult()) { if (rdr.Read()) { pagedResult.TotalCount = (int)rdr["TotalRecords"]; } }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the detailed exception to a text file
                common.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                // Return an empty result to the UI to prevent a crash
            }
           
        var jsonResult = new JsonResult
            {
                Data = pagedResult,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
                MaxJsonLength = int.MaxValue
            };

            return jsonResult;
        }

        [HttpGet]
public JsonResult GetVehicleDetails(string id) // 'id' here is the SerialNumber
        {
            var viewModel = new VehicleDetailWithReadingsViewModel
            {
                // Initialize the list to avoid null issues
                Readings = new List<ProductReading>()
            };

            try
            {
                using (var con = new SqlConnection(GetConnectionString()))
                {
                    // This SP returns two tables: one for the vehicle, one for the readings
                    using (var cmd = new SqlCommand("USP_READvehicleproduct", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SerialNumber", id);
                        con.Open();

                        using (var rdr = cmd.ExecuteReader())
                        {
                            // --- Read the first result set (the single Vehicle Info) ---
                            if (rdr.Read())
                            {
                                viewModel.VehicleInfo = new FullDetailViewModel
                                {
                                    SerialNumber = GetValue<string>(rdr, "SerialNumber"),
                                    Brand = GetValue<string>(rdr, "Brand"),
                                    Model_Vehicle = GetValue<string>(rdr, "Model"),
                                    ServiceEngineer = GetValue<string>(rdr, "ServiceEngineer"),
                                    PrimarySegment = GetValue<string>(rdr, "PrimarySegment"),
                                    SaleDate = GetValue<DateTimeOffset?>(rdr, "SaleDate"),
                                    AverageHMR = GetValue<decimal?>(rdr, "AverageHMR"),
                                    SaleszorderNumber = GetValue<string>(rdr, "SaleszorderNumber"),
                                    Nextservicetype = GetValue<string>(rdr, "Nextservicetype"),
                                    Isactive = GetValue<bool?>(rdr, "Isactive"),
                                    LastHMRupdateddate = GetValue<DateTimeOffset?>(rdr, "LastHMRupdateddate"),
                                    Isimported = GetValue<bool?>(rdr, "Isimported"),
                                    Remarks = GetValue<string>(rdr, "Remarks"),
                                    Operatorname = GetValue<string>(rdr, "Operatorname"),
                                    Producttype = GetValue<string>(rdr, "Producttype"),
                                    Machinestatus = GetValue<string>(rdr, "Machinestatus"),
                                    Secondarysegment = GetValue<string>(rdr, "Secondarysegment"),
                                    Commissioningdate = GetValue<DateTimeOffset?>(rdr, "Commissioningdate"),
                                    MachineHMR = GetValue<short?>(rdr, "MachineHMR"),
                                    Lastservicedate = GetValue<DateTimeOffset?>(rdr, "Lastservicedate"),
                                    Cuurentsiteaddress = GetValue<string>(rdr, "Cuurentsiteaddress"),
                                    nextservicedate = GetValue<DateTimeOffset?>(rdr, "nextservicedate"),
                                    VehicleWarranty = GetValue<string>(rdr, "VehicleWarranty")
                                };
                            }

                            // --- Move to the next result set (the list of Readings) ---
                            if (rdr.NextResult())
                            {
                                while (rdr.Read())
                                {
                                    viewModel.Readings.Add(new ProductReading
                                    {
                                        Company = GetValue<string>(rdr, "Company"),
                                        Mode = GetValue<string>(rdr, "Mode"),
                                        ReferenceNumber = GetValue<string>(rdr, "ReferenceNumber"),
                                        SerialNumber = GetValue<string>(rdr, "SerialNumber"),
                                        ReferenceDate = GetValue<DateTimeOffset>(rdr, "ReferenceDate")
                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                common.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Json(viewModel, JsonRequestBehavior.AllowGet);
        }
        // Private helper to add all parameters to the command object
        private void AddAllParameters(SqlCommand cmd, FullDetailViewModel model)
        {
            
            cmd.Parameters.AddWithValue("@Company", (object)model.Company ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Mode_Reading", (object)model.Mode_Reading ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@ReferenceNumber", (object)model.ReferenceNumber ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@ReferenceDate", model.ReferenceDate);
         
            cmd.Parameters.AddWithValue("@SerialNumber", (object)model.SerialNumber ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Brand", (object)model.Brand ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Model_Vehicle", (object)model.Model_Vehicle ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@ServiceEngineer", (object)model.ServiceEngineer ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@PrimarySegment", (object)model.PrimarySegment ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@SaleDate", (object)model.SaleDate ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@AverageHMR", (object)model.AverageHMR ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@SaleszorderNumber", (object)model.SaleszorderNumber ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Nextservicetype", (object)model.Nextservicetype ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Isactive", (object)model.Isactive ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@LastHMRupdateddate", (object)model.LastHMRupdateddate ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Isimported", (object)model.Isimported ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Remarks", (object)model.Remarks ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Operatorname", (object)model.Operatorname ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Producttype", (object)model.Producttype ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Machinestatus", (object)model.Machinestatus ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Secondarysegment", (object)model.Secondarysegment ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Commissioningdate", (object)model.Commissioningdate ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@MachineHMR", (object)model.MachineHMR ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Lastservicedate", (object)model.Lastservicedate ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Cuurentsiteaddress", (object)model.Cuurentsiteaddress ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@nextservicedate", (object)model.nextservicedate ?? DBNull.Value);
        }

      
        [HttpPost]
        public JsonResult Create(VehicleDetailWithReadingsViewModel model)
        {
            try
            {
                var readingsTable = new DataTable();
                readingsTable.Columns.Add("Company", typeof(string));
                readingsTable.Columns.Add("Mode", typeof(string));
                readingsTable.Columns.Add("ReferenceNumber", typeof(string));
                readingsTable.Columns.Add("SerialNumber", typeof(string));
                readingsTable.Columns.Add("ReferenceDate", typeof(DateTimeOffset));

                foreach (var reading in model.Readings)
                {
                    readingsTable.Rows.Add(reading.Company, reading.Mode, reading.ReferenceNumber, reading.SerialNumber, reading.ReferenceDate);
                }
                using (SqlConnection con = new SqlConnection(GetConnectionString()))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_CRTvehicleproduct", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SerialNumber", model.VehicleInfo.SerialNumber);
                        cmd.Parameters.AddWithValue("@Brand", (object)model.VehicleInfo.Brand ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Model_Vehicle", (object)model.VehicleInfo.Model_Vehicle ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ServiceEngineer", (object)model.VehicleInfo.ServiceEngineer ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PrimarySegment", (object)model.VehicleInfo.PrimarySegment ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@SaleDate", (object)model.VehicleInfo.SaleDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@AverageHMR", (object)model.VehicleInfo.AverageHMR ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@SaleszorderNumber", (object)model.VehicleInfo.SaleszorderNumber ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Nextservicetype", (object)model.VehicleInfo.Nextservicetype ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Isactive", (object)model.VehicleInfo.Isactive ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@LastHMRupdateddate", (object)model.VehicleInfo.LastHMRupdateddate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Isimported", (object)model.VehicleInfo.Isimported ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Remarks", (object)model.VehicleInfo.Remarks ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Operatorname", (object)model.VehicleInfo.Operatorname ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Producttype", (object)model.VehicleInfo.Producttype ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Machinestatus", (object)model.VehicleInfo.Machinestatus ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Secondarysegment", (object)model.VehicleInfo.Secondarysegment ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Commissioningdate", (object)model.VehicleInfo.Commissioningdate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MachineHMR", (object)model.VehicleInfo.MachineHMR ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Lastservicedate", (object)model.VehicleInfo.Lastservicedate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Cuurentsiteaddress", (object)model.VehicleInfo.Cuurentsiteaddress ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@nextservicedate", (object)model.VehicleInfo.nextservicedate ?? DBNull.Value);
                        SqlParameter tvpParam = cmd.Parameters.AddWithValue("@Readings", readingsTable);
                        tvpParam.SqlDbType = SqlDbType.Structured;
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
                return Json(new { success = true, message = "Record created successfully." });
            }
            catch (Exception ex)
            {
                common.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace); return Json(new { success = false, message = ex.Message });
            }
        }

        
        [HttpPost]
        public JsonResult Update(VehicleDetailWithReadingsViewModel model)
        {
            try
            {
                var readingsTable = new DataTable();
                readingsTable.Columns.Add("Company", typeof(string));
                readingsTable.Columns.Add("Mode", typeof(string));
                readingsTable.Columns.Add("ReferenceNumber", typeof(string));
                readingsTable.Columns.Add("SerialNumber", typeof(string));
                readingsTable.Columns.Add("ReferenceDate", typeof(DateTimeOffset));

                // Loop through the readings from the form and add them to our DataTable
                foreach (var reading in model.Readings)
                {
                    readingsTable.Rows.Add(reading.Company, reading.Mode, reading.ReferenceNumber, reading.SerialNumber, reading.ReferenceDate);
                }
                using (SqlConnection con = new SqlConnection(GetConnectionString()))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_UPDvehicleproduct", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                   
                        cmd.Parameters.AddWithValue("@SerialNumber", model.VehicleInfo.SerialNumber);
                        cmd.Parameters.AddWithValue("@Brand", (object)model.VehicleInfo.Brand ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Model_Vehicle", (object)model.VehicleInfo.Model_Vehicle ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ServiceEngineer", (object)model.VehicleInfo.ServiceEngineer ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PrimarySegment", (object)model.VehicleInfo.PrimarySegment ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@SaleDate", (object)model.VehicleInfo.SaleDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@AverageHMR", (object)model.VehicleInfo.AverageHMR ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@SaleszorderNumber", (object)model.VehicleInfo.SaleszorderNumber ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Nextservicetype", (object)model.VehicleInfo.Nextservicetype ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Isactive", (object)model.VehicleInfo.Isactive ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@LastHMRupdateddate", (object)model.VehicleInfo.LastHMRupdateddate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Isimported", (object)model.VehicleInfo.Isimported ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Remarks", (object)model.VehicleInfo.Remarks ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Operatorname", (object)model.VehicleInfo.Operatorname ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Producttype", (object)model.VehicleInfo.Producttype ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Machinestatus", (object)model.VehicleInfo.Machinestatus ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Secondarysegment", (object)model.VehicleInfo.Secondarysegment ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Commissioningdate", (object)model.VehicleInfo.Commissioningdate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MachineHMR", (object)model.VehicleInfo.MachineHMR ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Lastservicedate", (object)model.VehicleInfo.Lastservicedate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Cuurentsiteaddress", (object)model.VehicleInfo.Cuurentsiteaddress ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@nextservicedate", (object)model.VehicleInfo.nextservicedate ?? DBNull.Value);
                        SqlParameter tvpParam = cmd.Parameters.AddWithValue("@Readings", readingsTable);
                        tvpParam.SqlDbType = SqlDbType.Structured;
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
                return Json(new { success = true, message = "Record updated successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }



        [HttpPost]
       
        public JsonResult Delete(string id)
        {
            try
            {
                using (var con = new SqlConnection(GetConnectionString()))
                {
                    using (var cmd = new SqlCommand("USP_DELvehicleproduct", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SerialNumber", id);
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
                return Json(new { success = true, message = "Vehicle and all associated readings deleted successfully." });
            }
            catch (Exception ex)
            {
                // THIS IS THE FIX: We now send the REAL error message to the browser.
                return Json(new { success = false, message = ex.Message });
            }
        }

    }
}
    