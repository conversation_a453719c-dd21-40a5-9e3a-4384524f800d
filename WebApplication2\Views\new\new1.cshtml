﻿
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>new1</title>
    <link href="~/Assets/css/styles.css" rel="stylesheet" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
</head>
<body>
    <div>
        <button onclick="dataa()">
            Getdata from the DB
        </button>
        <button onclick=" dataaload()">
            load to table
        </button>
        <div id="dataContainer" style="margin-top: 20px; border-block: 2px; border: solid; border-collapse: collapse;"></div>
        <label> @HttpContext.GetGlobalResourceObject("sample", "data_SP")</label>
        <script>
            function dataa() {

                $.ajax({
                    url: "New/Getnew1",
                    success: function (result) {

                        debugger
                        console.log(result)
                    },
                    Error: function () {
                        alert("error");
                    }
                });
            }
            function dataaload() {
                $.ajax({
                    url: "/New/Getnew1", // Your controller URL
                    success: function (result) {
                        let container = $('#dataContainer');
                        container.empty(); // Clear previous results

                        // Check if any data was returned
                        if (!result || result.length === 0) {
                            container.html("<p>No data found.</p>");
                            return;
                        }

                        // 1. Create the table element
                        let table = $('<table class="styled-table"></table>');

                        // 2. Create the table header from the object keys
                        let thead = $('<thead><tr></tr></thead>');
                        Object.keys(result[0]).forEach(function (key) {
                            thead.find('tr').append($('<th></th>').text(key));
                        });
                        table.append(thead);

                        // 3. Create the table body and fill it with data
                        let tbody = $('<tbody></tbody>');
                        result.forEach(function (rowData) {
                            let row = $('<tr></tr>');
                            Object.values(rowData).forEach(function (value) {
                                row.append($('<td></td>').text(value));
                            });
                            tbody.append(row);
                        });
                        table.append(tbody);

                        // 4. Add the completed table to the page
                        container.append(table);
                    },
                    error: function (xhr, status, error) {
                        alert("An error occurred: " + status + " " + error);
                    }
                });
            }

        </script>


        })
    </div>
</body>
</html>
