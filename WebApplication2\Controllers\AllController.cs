﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Web.Mvc;
using WebApplication2.Models; // Ensure this matches your project's namespace

namespace WebApplication2.Controllers
{
    public class AllController : Controller
    {
        public ActionResult Allcode()
        {
            return View();
        }

        [HttpGet]
        public JsonResult GetData(int? userId = null) // Re-added optional filter
        {
            var users = new List<User>();
            string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(constr))
            {
                string query = "SELECT UserID, FirstName, LastName, Email, DateCreated FROM Users";
                if (userId.HasValue)
                {
                    query += " WHERE UserID = @UserID";
                }
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    if (userId.HasValue)
                    {
                        cmd.Parameters.AddWithValue("@UserID", userId.Value);
                    }
                    con.Open();
                    using (SqlDataReader rdr = cmd.ExecuteReader())
                    {
                        while (rdr.Read())
                        {
                            users.Add(new User
                            {
                                // Added DBNull checks to prevent crashes
                                UserID = rdr["UserID"] != DBNull.Value ? Convert.ToInt32(rdr["UserID"]) : 0,
                                FirstName = rdr["FirstName"] != DBNull.Value ? rdr["FirstName"].ToString() : "",
                                LastName = rdr["LastName"] != DBNull.Value ? rdr["LastName"].ToString() : "",
                                Email = rdr["Email"] != DBNull.Value ? rdr["Email"].ToString() : "",
                                DateCreated = rdr["DateCreated"] != DBNull.Value ? Convert.ToDateTime(rdr["DateCreated"]) : DateTime.MinValue
                            });
                        }
                    }
                }
            }
            return Json(users, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        public JsonResult GetUserIds()
        {
            var userIds = new List<int>();
            string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(constr))
            {
                string query = "SELECT UserID FROM Users ORDER BY UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    con.Open();
                    using (SqlDataReader rdr = cmd.ExecuteReader())
                    {
                        while (rdr.Read())
                        {
                            userIds.Add(Convert.ToInt32(rdr["UserID"]));
                        }
                    }
                }
            }
            return Json(userIds, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult CreateUser(User user) // Changed to accept a User object
        {
            string passwordHash = YourPasswordHashingFunction(user.PasswordHash); // Assume password is in PasswordHash field for create
            string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(constr))
            {
                string query = @"INSERT INTO Users (FirstName, LastName, Email, PasswordHash, DateCreated) 
                                 VALUES (@FirstName, @LastName, @Email, @PasswordHash, @DateCreated)";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@FirstName", user.FirstName);
                    cmd.Parameters.AddWithValue("@LastName", user.LastName);
                    cmd.Parameters.AddWithValue("@Email", user.Email);
                    cmd.Parameters.AddWithValue("@PasswordHash", passwordHash);
                    cmd.Parameters.AddWithValue("@DateCreated", DateTime.Now);
                    con.Open();
                    cmd.ExecuteNonQuery();
                }
            }
            return Json(new { success = true, message = "User created successfully!" });
        }

        [HttpPost]
        public JsonResult UpdateUser(User user) // Accepts a User object
        {
            string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(constr))
            {
                // Ensure you are updating all the necessary fields
                string query = "UPDATE Users SET FirstName = @FirstName, LastName = @LastName, Email = @Email WHERE UserID = @UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", user.UserID);
                    cmd.Parameters.AddWithValue("@FirstName", user.FirstName);
                    cmd.Parameters.AddWithValue("@LastName", user.LastName);
                    cmd.Parameters.AddWithValue("@Email", user.Email);
                    con.Open();
                    cmd.ExecuteNonQuery();
                }
            }
            return Json(new { success = true, message = "User updated successfully!" });
        }

        [HttpPost]
        public JsonResult DeleteUser(int UserID) // Parameter name is "UserID"
        {
            string constr = ConfigurationManager.ConnectionStrings["NEWDB"].ConnectionString;
            using (SqlConnection con = new SqlConnection(constr))
            {
                string query = "DELETE FROM Users WHERE UserID = @UserID";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@UserID", UserID);
                    con.Open();
                    cmd.ExecuteNonQuery();
                }
            }
            return Json(new { success = true, message = "User deleted successfully!" });
        }

        private string YourPasswordHashingFunction(string password)
        {
            if (string.IsNullOrEmpty(password)) return null;
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));
                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }
    }
}