$(function () {
    let allVehicles = [];
    let currentView = 'list'; // Default view

    // --- HELPER FUNCTIONS ---
    function showToast(message, type = 'success') {
        const toast = $(`<div class="toast ${type}">${message}</div>`);
        $('#toastContainer').append(toast);
        setTimeout(() => toast.addClass('show'), 100);
        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => toast.remove(), 500);
        }, 3000);
    }

    function formatDateForInput(msDate) {
        if (!msDate) return '';
        const date = new Date(parseInt(msDate.substr(6)));
        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        return `${year}-${month}-${day}`;
    }

    // --- DATA LOADING & RENDERING ---
    function loadData() {
        $.ajax({
            url: '/Vehicle/GetVehicleDetails',
            type: 'GET',
            //data: { serialNumber: 1 },
            success: function (vehicles) {
                debugger
                allVehicles = vehicles;
                applyFiltersAndRender();
            },
            error: function () {
                showToast('Failed to load vehicle data from the server.', 'error');
            }
        });

    //    $.ajax({
    //        url: '/Vehicle/GetStatusOptions',
    //        type: 'GET',
    //        success: function (statuses) {
    //            const container = $('#statusFilterContainer');
    //            container.html('<button class="filter-pill active" data-status="">All</button>');
    //            statuses.forEach(status => {
    //                container.append(`<button class="filter-pill" data-status="${status}">${status}</button>`);
    //            });
    //        },
    //        error: function () {
    //            console.error("Failed to load status options.");
    //        }
    //    });
    }

    function applyFiltersAndRender() {
        let filteredVehicles = allVehicles;
        const activeStatus = $('#statusFilterContainer .active').data('status');
        if (activeStatus) {
            filteredVehicles = filteredVehicles.filter(v => v.Machinestatus === activeStatus);
        }
        render(filteredVehicles);
    }

    function render(vehicles) {
        if (currentView === 'table') {
            renderTableView(vehicles);
        } else if (currentView === 'card') {
            renderCardView(vehicles);
        } else {
            renderListView(vehicles);
        }
    }

    function renderTableView(vehicles) {
        const container = $('#dataContainer');
        container.empty();
        if (vehicles.length === 0) { container.html("<p>No vehicles found.</p>"); return; }
        let table = $('<table class="table"></table>');
        table.append('<thead><tr><th>Serial Number</th><th>Brand</th><th>Model</th><th>Status</th><th>Actions</th></tr></thead>');
        let tbody = $('<tbody></tbody>');
        vehicles.forEach(v => {
            tbody.append(`
                <tr data-serial="${v.SerialNumber}">
                    <td>${v.SerialNumber}</td><td>${v.Brand}</td><td>${v.Model}</td>
                    <td><span class="status-pill ${v.Machinestatus.toLowerCase()}">${v.Machinestatus}</span></td>
                    <td>
                        <i class="fas fa-eye action-icon view-btn" title="View"></i>
                        <i class="fas fa-edit action-icon edit-btn" title="Edit"></i>
                        <i class="fas fa-trash action-icon delete-btn" title="Delete"></i>
                    </td>
                </tr>`);
        });
        table.append(tbody);
        container.html(table);
    }

    function renderCardView(vehicles) {
        const container = $('#dataContainer');
        container.empty();
        if (vehicles.length === 0) { container.html("<p>No vehicles found.</p>"); return; }
        let cardContainer = $('<div class="card-view-container"></div>');
        vehicles.forEach(v => {
            cardContainer.append(`
                <div class="vehicle-card" data-serial="${v.SerialNumber}">
                    <div class="card-header"><span>${v.SerialNumber}</span><span class="status-pill ${v.Machinestatus.toLowerCase()}">${v.Machinestatus}</span></div>
                    <div class="card-body"><h4>${v.Brand} ${v.Model}</h4></div>
                    <div class="card-footer">
                        
                        <div>
                            <i class="fas fa-eye action-icon view-btn" title="View"></i>
                            <i class="fas fa-edit action-icon edit-btn" title="Edit"></i>
                            <i class="fas fa-trash action-icon delete-btn" title="Delete"></i>
                        </div>
                    </div>
                </div>`);
        });
        container.html(cardContainer);
    }

    function renderListView(vehicles) {
        const container = $('#dataContainer');
        container.empty();
        if (vehicles.length === 0) { container.html("<p>No vehicles found.</p>"); return; }
        let listContainer = $('<div class="list-view-container"></div>');
        vehicles.forEach(v => {
            const brandInitial = v.Brand ? v.Brand.charAt(0).toUpperCase() : '?';
            listContainer.append(`
                <div class="list-view-item" data-serial="${v.SerialNumber}">
                    <div class="list-avatar">${brandInitial}</div>
                    <div class="list-col">
                        <span class="list-main-title">${v.SerialNumber}</span>
                        <span class="list-subtitle">${v.Brand} ${v.Model}</span>
                    </div>
                    <div class="list-col">
                        <div class="list-data-pair"><span class="label">Status:</span><span class="value">${v.Machinestatus}</span></div>
                        <div class="list-data-pair"><span class="label">Type:</span><span class="value">${v.Producttype}</span></div>
                    </div>
                    <div class="list-col">
                        <div class="list-data-pair"><span class="label">Sale Date:</span><span class="value">${new Date(parseInt(v.SaleDate.substr(6))).toLocaleDateString()}</span></div>
                        <div class="list-data-pair"><span class="label">Service Due:</span><span class="value">${new Date(parseInt(v.nextservicedate.substr(6))).toLocaleDateString()}</span></div>
                    </div>
                    <div class="list-actions">
                        <i class="fas fa-eye action-icon view-btn" title="View"></i>
                        <i class="fas fa-edit action-icon edit-btn" title="Edit"></i>
                        <i class="fas fa-trash action-icon delete-btn" title="Delete"></i>
                    </div>
                </div>`);
        });
        container.html(listContainer);
    }

    function openModal(mode, serialNumber = null) {
        const modal = $('#vehicleModal');
        const modalBody = modal.find('.modal-body');

        // Load the partial view containing the form structure
        modalBody.load('/Vehicle/VehicleForm', function () {
            $('#modalTitle').text(mode + ' Vehicle');

            if (mode === 'Add') {
                // For "Add", the form is blank and editable
                $('#vehicleForm fieldset').prop('disabled', false);
                $('#saveBtn').show();
                modal.show();
            } else {
                $.ajax({
                    url: '/Vehicle/GetVehicleDetails',
                    type: 'GET',
                    data: { serialNumber: serialNumber },
                    success: function (vehicle) {
                        if (!vehicle) { showToast('Could not load vehicle details.', 'error'); return; }

                        $('#SerialNumber').val(vehicle.SerialNumber);
                        $('#Brand').val(vehicle.Brand);
                        $('#Model').val(vehicle.Model);
                        $('#ServiceEngineer').val(vehicle.ServiceEngineer);
                        $('#PrimarySegment').val(vehicle.PrimarySegment);
                        $('#SaleDate').val(formatDateForInput(vehicle.SaleDate));
                        $('#AverageHMR').val(vehicle.AverageHMR);
                        $('#SaleszorderNumber').val(vehicle.SaleszorderNumber);
                        $('#Nextservicetype').val(vehicle.Nextservicetype);
                        $('#Isactive').prop('checked', vehicle.Isactive);
                        $('#LastHMRupdateddate').val(formatDateForInput(vehicle.LastHMRupdateddate));
                        $('#Isimported').prop('checked', vehicle.Isimported);
                        $('#Remarks').val(vehicle.Remarks);
                        $('#Operatorname').val(vehicle.Operatorname);
                        $('#Producttype').val(vehicle.Producttype);
                        $('#Machinestatus').val(vehicle.Machinestatus);
                        $('#Secondarysegment').val(vehicle.Secondarysegment);
                        $('#Commissioningdate').val(formatDateForInput(vehicle.Commissioningdate));
                        $('#MachineHMR').val(vehicle.MachineHMR);
                        $('#Lastservicedate').val(formatDateForInput(vehicle.Lastservicedate));
                        $('#Cuurentsiteaddress').val(vehicle.Cuurentsiteaddress);
                        $('#nextservicedate').val(formatDateForInput(vehicle.nextservicedate));


                        if (mode === 'View') {
                            $('#vehicleForm fieldset').prop('disabled', true);
                            $('#saveBtn').hide();
                        } else { // Edit mode
                            $('#vehicleForm fieldset').prop('disabled', false);
                            $('#saveBtn').show();
                        }
                        modal.show();
                    },
                    error: function () {
                        showToast('Error fetching vehicle details.', 'error');
                    }
                });
            }
        });
    }

    // --- EVENT HANDLERS ---
    $('.view-toggles').on('click', '.view-btn', function () {
        $('.view-toggles .view-btn').removeClass('active');
        $(this).addClass('active');
        currentView = $(this).data('view');
        applyFiltersAndRender();
    });

    $('#statusFilterContainer').on('click', '.filter-pill', function () {
        $('#statusFilterContainer .filter-pill').removeClass('active');
        $(this).addClass('active');
        applyFiltersAndRender();
    });

    $('#addVehicleBtn').on('click', () => openModal('Add'));

    $('#dataContainer').on('click', '.view-btn', function () { openModal('View', $(this).closest('[data-serial]').data('serial')); });
    $('#dataContainer').on('click', '.edit-btn', function () { openModal('Edit', $(this).closest('[data-serial]').data('serial')); });

    $('#dataContainer').on('click', '.delete-btn', function () {
        const serial = $(this).closest('[data-serial]').data('serial');
        if (confirm(`Are you sure you want to delete vehicle ${serial}?`)) {
            $.ajax({
                url: '/Vehicle/DeleteVehicle',
                type: 'POST',
                data: { serialNumber: serial },
                success: function (response) {
                    showToast(response.message, response.success ? 'success' : 'error');
                    if (response.success) loadData();
                },
                error: function () {
                    showToast('An error occurred while deleting.', 'error');
                }
            });
        }
    });

    $('#closeModalBtn, #cancelModalBtn').on('click', () => $('#vehicleModal').hide());

    $(document).on('submit', '#vehicleForm', function (e) {
        e.preventDefault();
        const serial = $('#SerialNumber').val();
        const url = (serial && serial !== "") ? '/Vehicle/UpdateVehicle' : '/Vehicle/CreateVehicle';

        $.ajax({
            url: url,
            type: 'POST',
            data: $(this).serialize(),
            success: function (response) {
                showToast(response.message, response.success ? 'success' : 'error');
                if (response.success) {
                    $('#vehicleModal').hide();
                    loadData();
                }
            },
            error: function () {
                showToast('An error occurred while saving.', 'error');
            }
        });
    });

    // --- INITIAL LOAD ---
    loadData();
});