﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="active_status" xml:space="preserve">
    <value>Is Active</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Add_new" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="averageHmr" xml:space="preserve">
    <value>Average HMR</value>
  </data>
  <data name="Back_Button" xml:space="preserve">
    <value>Back </value>
  </data>
  <data name="Brand_details" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="Commissioning_date" xml:space="preserve">
    <value>Commissioning Date</value>
  </data>
  <data name="Company_ID" xml:space="preserve">
    <value>Company ID</value>
  </data>
  <data name="data_SP" xml:space="preserve">
    <value>data</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Delete_selected" xml:space="preserve">
    <value>Delete Selected</value>
  </data>
  <data name="Edit_data" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="FunctionGroup_ID" xml:space="preserve">
    <value>FunctionGroup ID</value>
  </data>
  <data name="imported" xml:space="preserve">
    <value>Is Imported</value>
  </data>
  <data name="Last_HMR" xml:space="preserve">
    <value>Last HMR Updated Date</value>
  </data>
  <data name="Last_servivedate" xml:space="preserve">
    <value>Last Service Date</value>
  </data>
  <data name="Machine_hmr" xml:space="preserve">
    <value>Machine HMR</value>
  </data>
  <data name="Machine_status" xml:space="preserve">
    <value>Machine Status</value>
  </data>
  <data name="Model_details" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="next_service" xml:space="preserve">
    <value>Next Service Type</value>
  </data>
  <data name="Next_serviceDate" xml:space="preserve">
    <value>Next Service Date</value>
  </data>
  <data name="Operators_name" xml:space="preserve">
    <value>Operator Name</value>
  </data>
  <data name="Primary_Segment" xml:space="preserve">
    <value>Primary segment</value>
  </data>
  <data name="product_type" xml:space="preserve">
    <value>Product Type</value>
  </data>
  <data name="Remarks_Data" xml:space="preserve">
    <value>Remarks</value>
  </data>
  <data name="Sales_date" xml:space="preserve">
    <value>Sale Date</value>
  </data>
  <data name="Sales_order_number" xml:space="preserve">
    <value>Sales Order Number</value>
  </data>
  <data name="Save_changes" xml:space="preserve">
    <value>Save Changes</value>
  </data>
  <data name="Secondary_segment" xml:space="preserve">
    <value>Secondary Segment</value>
  </data>
  <data name="Serial_number" xml:space="preserve">
    <value>Serial Number </value>
  </data>
  <data name="Service_engineer_name" xml:space="preserve">
    <value>Service Engineer</value>
  </data>
  <data name="site_name" xml:space="preserve">
    <value>Current Site Address</value>
  </data>
  <data name="Vehicle_List" xml:space="preserve">
    <value>Vehicle List</value>
  </data>
</root>