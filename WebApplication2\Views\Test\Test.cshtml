﻿
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Test</title>
    <link href="~/Assets/css/styles.css" rel="stylesheet" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
</head>
<body>
    <div>
        <h3>Read Data from Database</h3>
        <button id="getDataBtn">Get Data</button>
        <div id="tableContainer" style="margin-top:20px;"></div>
        <h3>Create New User</h3>

        <div class="form-group">
            <label>First Name:</label>
            <input type="text" id="firstName" class="form-control" />
        </div>
        <div class="form-group">
            <label>Last Name:</label>
            <input type="text" id="lastName" class="form-control" />
        </div>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" id="email" class="form-control" />
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="password" class="form-control" />
        </div>

        <button id="createUserBtn">Create User</button>
        <div id="statusMessage" style="margin-top: 15px;"></div>

        @*update code*@
        <h3>Update Existing User</h3>

        <div class="form-group">
            <label>User ID to Update:</label>
            <input type="text" id="updateUserId" class="form-control" />
        </div>
        <div class="form-group">
            <label>New First Name:</label>
            <input type="text" id="updateFirstName" class="form-control" />
        </div>
        <div class="form-group">
            <label>New Last Name:</label>
            <input type="text" id="updateLastName" class="form-control" />
        </div>
        <div class="form-group">
            <label>New Email:</label>
            <input type="email" id="updateEmail" class="form-control" />
        </div>

        <button id="updateUserBtn" class="btn btn-info">Update User</button>
        <div id="updateStatus" style="margin-top: 15px;"></div>

        @*Deletecode*@
        <h3>Delete User</h3>

        <div class="form-group">
            <label>User ID to Delete:</label>
            <input type="text" id="deleteUserId" class="form-control" />
        </div>

        <button id="deleteUserBtn" class="btn btn-danger">Delete User</button>
        <div id="deleteStatus" style="margin-top: 15px;"></div>
        <script>
            $("#getDataBtn").on("click", function () {
                $.ajax({
                    url: "Test/GetData", //
                    type: "GET",
                    success: function (result) {
                        let container = $('#tableContainer');
                        container.empty();
                        if (result.length === 0) {
                            container.html("No data found.");
                            return;
                        }
                        let table = $('<table class="styled-table"></table>');
                        let thead = $('<thead><tr></tr></thead>');
                        Object.keys(result[0]).forEach(key => thead.find('tr').append(`<th>${key}</th>`));
                        table.append(thead);

                        let tbody = $('<tbody></tbody>');
                        result.forEach(rowData => {
                            let row = $('<tr></tr>');
                            Object.values(rowData).forEach(value => row.append(`<td>${value}</td>`));
                            tbody.append(row);
                        });
                        table.append(tbody);
                        container.append(table);
                    },
                    error: function () { alert("Error fetching data."); }
                });
            });
            $(function () {
                $("#createUserBtn").on("click", function () {
                    debugger
                    let userData = {
                        FirstName: $("#firstName").val(),
                        LastName: $("#lastName").val(),
                        Email: $("#email").val(),
                        Password: $("#password").val() // We send the plain password to the server to be hashed
                    };

                    $.ajax({
                        url: "Test/CreateUser", //
                        type: "POST",
                        data: userData,
                        success: function (response) {
                            $("#statusMessage").html(`<p style="color:green;">${response.message}</p>`);
                        },
                        error: function (xhr) {
                            let errorMsg = xhr.responseJSON ? xhr.responseJSON.message : "An error occurred.";
                            $("#statusMessage").html(`<p style="color:red;">Error: ${errorMsg}</p>`);
                        }
                    });
                });
            });
            @*updatecode*@

            $("#updateUserBtn").on("click", function () {
                let userData = {
                    UserID: $("#updateUserId").val(),
                    FirstName: $("#updateFirstName").val(),
                    LastName: $("#updateLastName").val(),
                    Email: $("#updateEmail").val()
                };

                if (!userData.UserID) {
                    alert("Please provide a User ID to update.");
                    return;
                }

                $.ajax({
                    url: "Test/UpdateUser", //
                    type: "POST",
                    data: userData,
                    success: function (response) {
                        $("#updateStatus").html(`<p style="color:blue;">${response.message}</p>`);
                    },
                    error: function (xhr) {
                        let errorMsg = xhr.responseJSON ? xhr.responseJSON.message : "An error occurred.";
                        $("#updateStatus").html(`<p style="color:red;">Error: ${errorMsg}</p>`);
                    }
                });
            });
            @*deletecode*@
            $("#deleteUserBtn").on("click", function () {
                let userIdToDelete = $("#deleteUserId").val();

                if (!userIdToDelete) {
                    alert("Please provide a User ID to delete.");
                    return;
                }

                // IMPORTANT: Always ask for confirmation before deleting
                if (confirm("Are you sure you want to delete this user? This cannot be undone.")) {
                    $.ajax({
                        url: "Test/DeleteUser", // Change to your controller/action name
                        type: "POST",
                        data: { UserID: userIdToDelete },
                        success: function (response) {
                            $("#deleteStatus").html(`<p style="color:red;">${response.message}</p>`);
                        },
                        error: function (xhr) {
                            let errorMsg = xhr.responseJSON ? xhr.responseJSON.message : "An error occurred.";
                            $("#deleteStatus").html(`<p style="color:red;">Error: ${errorMsg}</p>`);
                        }
                    });
                }
            });

        </script>
    </div>
</body>
</html>
