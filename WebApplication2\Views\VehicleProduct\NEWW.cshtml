@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vehicle Dashboard</title>
    <link href="~/Assets/css/new-styles.css" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>

    <div class="main-container">
        <header class="main-header">
            <h1>Vehicle Dashboard</h1>
            <button id="btn-create" class="action-btn">+Add</button>
        </header>

        <div class="controls">
            <div class="view-switcher">
                <button id="btn-grid" class="view-btn active">Grid</button>
                <button id="btn-card" class="view-btn">Card</button>
                <button id="btn-list" class="view-btn">List</button>
                <button id="btn-compact" class="view-btn">Compact</button>
            </div>
        </div>

        <div class="grid-header">
            <div class="header-cell">Vehicle (Brand & Model)</div>
            <div class="header-cell">Serial Number</div>
            <div class="header-cell">Status</div>
            <div class="header-cell">Last Reading Ref #</div>
            <div class="header-cell">Actions</div>
        </div>

        <div id="data-container" class="grid-view">
        </div>

        <div id="paging-container" class="paging-controls">
        </div>
    </div>

    <div id="form-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2 id="form-modal-title"></h2>
            <form id="readingForm">
                <div class="form-group full-width">
                    <div id="VehicleWarranty" class="warranty-status"></div>
                </div>

                <div class="form-group">
                    <label for="ReferenceNumber">Reference Number</label>
                    <input type="text" id="ReferenceNumber" name="ReferenceNumber">
                </div>
                <div class="form-group">
                    <label for="Company">Company</label>
                    <input type="text" id="Company" name="Company">
                </div>
                <div class="form-group">
                    <label for="Mode_Reading">Reading Mode</label>
                    <input type="text" id="Mode_Reading" name="Mode_Reading">
                </div>
                <div class="form-group">
                    <label for="ReferenceDate">Reference Date</label>
                    <input type="datetime-local" id="ReferenceDate" name="ReferenceDate">
                </div>

                <div class="form-group">
                    <label for="SerialNumber">Serial Number</label>
                    <input type="text" id="SerialNumber" name="SerialNumber">
                </div>
                <div class="form-group">
                    <label for="Brand">Brand</label>
                    <input type="text" id="Brand" name="Brand">
                </div>
                <div class="form-group">
                    <label for="Model_Vehicle">Vehicle Model</label>
                    <input type="text" id="Model_Vehicle" name="Model_Vehicle">
                </div>
                <div class="form-group">
                    <label for="ServiceEngineer">Service Engineer</label>
                    <input type="text" id="ServiceEngineer" name="ServiceEngineer">
                </div>
                <div class="form-group">
                    <label for="PrimarySegment">Primary Segment</label>
                    <input type="text" id="PrimarySegment" name="PrimarySegment">
                </div>
                <div class="form-group">
                    <label for="SaleDate">Sale Date</label>
                    <input type="datetime-local" id="SaleDate" name="SaleDate">
                </div>
                <div class="form-group">
                    <label for="AverageHMR">Average HMR</label>
                    <input type="number" id="AverageHMR" name="AverageHMR">
                </div>
                <div class="form-group">
                    <label for="SaleszorderNumber">Sales Order Number</label>
                    <input type="text" id="SaleszorderNumber" name="SaleszorderNumber">
                </div>
                <div class="form-group">
                    <label for="Nextservicetype">Next Service Type</label>
                    <input type="text" id="Nextservicetype" name="Nextservicetype">
                </div>
                <div class="form-group">
                    <label for="LastHMRupdateddate">Last HMR Updated</label>
                    <input type="datetime-local" id="LastHMRupdateddate" name="LastHMRupdateddate">
                </div>
                <div class="form-group">
                    <label for="Remarks">Remarks</label>
                    <input type="text" id="Remarks" name="Remarks">
                </div>
                <div class="form-group">
                    <label for="Operatorname">Operator Name</label>
                    <input type="text" id="Operatorname" name="Operatorname">
                </div>
                <div class="form-group">
                    <label for="Producttype">Product Type</label>
                    <input type="text" id="Producttype" name="Producttype">
                </div>
                <div class="form-group">
                    <label for="Machinestatus">Machine Status</label>
                    <input type="text" id="Machinestatus" name="Machinestatus">
                </div>
                <div class="form-group">
                    <label for="Secondarysegment">Secondary Segment</label>
                    <input type="text" id="Secondarysegment" name="Secondarysegment">
                </div>
                <div class="form-group">
                    <label for="Commissioningdate">Commissioning Date</label>
                    <input type="datetime-local" id="Commissioningdate" name="Commissioningdate">
                </div>
                <div class="form-group">
                    <label for="MachineHMR">Machine HMR</label>
                    <input type="number" id="MachineHMR" name="MachineHMR">
                </div>
                <div class="form-group">
                    <label for="Lastservicedate">Last Service Date</label>
                    <input type="datetime-local" id="Lastservicedate" name="Lastservicedate">
                </div>
                <div class="form-group">
                    <label for="Cuurentsiteaddress">Current Site Address</label>
                    <input type="text" id="Cuurentsiteaddress" name="Cuurentsiteaddress">
                </div>
                <div class="form-group">
                    <label for="nextservicedate">Next Service Date</label>
                    <input type="datetime-local" id="nextservicedate" name="nextservicedate">
                </div>

                <div class="form-group checkbox-group">
                    <input type="checkbox" id="Isimported" name="Isimported">
                    <label for="Isimported">Is Imported</label>
                </div>
                <div class="form-group checkbox-group">
                    <input type="checkbox" id="Isactive" name="Isactive">
                    <label for="Isactive">Is Active</label>
                </div>

                <div class="form-group full-width">
                    <button type="submit" class="action-btn submit-btn">Save</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="~/Assets/js/Combine.js"></script>

</body>
</html>