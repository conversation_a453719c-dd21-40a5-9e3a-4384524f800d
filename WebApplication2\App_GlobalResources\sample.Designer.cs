//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class sample {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal sample() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.sample", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Active.
        /// </summary>
        internal static string active_status {
            get {
                return ResourceManager.GetString("active_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New.
        /// </summary>
        internal static string Add_new {
            get {
                return ResourceManager.GetString("Add_new", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average HMR.
        /// </summary>
        internal static string averageHmr {
            get {
                return ResourceManager.GetString("averageHmr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back .
        /// </summary>
        internal static string Back_Button {
            get {
                return ResourceManager.GetString("Back_Button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brand.
        /// </summary>
        internal static string Brand_details {
            get {
                return ResourceManager.GetString("Brand_details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commissioning Date.
        /// </summary>
        internal static string Commissioning_date {
            get {
                return ResourceManager.GetString("Commissioning_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company ID.
        /// </summary>
        internal static string Company_ID {
            get {
                return ResourceManager.GetString("Company_ID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to data.
        /// </summary>
        internal static string data_SP {
            get {
                return ResourceManager.GetString("data_SP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Selected.
        /// </summary>
        internal static string Delete_selected {
            get {
                return ResourceManager.GetString("Delete_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        internal static string Edit_data {
            get {
                return ResourceManager.GetString("Edit_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FunctionGroup ID.
        /// </summary>
        internal static string FunctionGroup_ID {
            get {
                return ResourceManager.GetString("FunctionGroup_ID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Imported.
        /// </summary>
        internal static string imported {
            get {
                return ResourceManager.GetString("imported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last HMR Updated Date.
        /// </summary>
        internal static string Last_HMR {
            get {
                return ResourceManager.GetString("Last_HMR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Service Date.
        /// </summary>
        internal static string Last_servivedate {
            get {
                return ResourceManager.GetString("Last_servivedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine HMR.
        /// </summary>
        internal static string Machine_hmr {
            get {
                return ResourceManager.GetString("Machine_hmr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Machine Status.
        /// </summary>
        internal static string Machine_status {
            get {
                return ResourceManager.GetString("Machine_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model.
        /// </summary>
        internal static string Model_details {
            get {
                return ResourceManager.GetString("Model_details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next Service Type.
        /// </summary>
        internal static string next_service {
            get {
                return ResourceManager.GetString("next_service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next Service Date.
        /// </summary>
        internal static string Next_serviceDate {
            get {
                return ResourceManager.GetString("Next_serviceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operator Name.
        /// </summary>
        internal static string Operators_name {
            get {
                return ResourceManager.GetString("Operators_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary segment.
        /// </summary>
        internal static string Primary_Segment {
            get {
                return ResourceManager.GetString("Primary_Segment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Type.
        /// </summary>
        internal static string product_type {
            get {
                return ResourceManager.GetString("product_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remarks.
        /// </summary>
        internal static string Remarks_Data {
            get {
                return ResourceManager.GetString("Remarks_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sale Date.
        /// </summary>
        internal static string Sales_date {
            get {
                return ResourceManager.GetString("Sales_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Number.
        /// </summary>
        internal static string Sales_order_number {
            get {
                return ResourceManager.GetString("Sales_order_number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Changes.
        /// </summary>
        internal static string Save_changes {
            get {
                return ResourceManager.GetString("Save_changes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secondary Segment.
        /// </summary>
        internal static string Secondary_segment {
            get {
                return ResourceManager.GetString("Secondary_segment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial Number .
        /// </summary>
        internal static string Serial_number {
            get {
                return ResourceManager.GetString("Serial_number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Engineer.
        /// </summary>
        internal static string Service_engineer_name {
            get {
                return ResourceManager.GetString("Service_engineer_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Site Address.
        /// </summary>
        internal static string site_name {
            get {
                return ResourceManager.GetString("site_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vehicle List.
        /// </summary>
        internal static string Vehicle_List {
            get {
                return ResourceManager.GetString("Vehicle_List", resourceCulture);
            }
        }
    }
}
