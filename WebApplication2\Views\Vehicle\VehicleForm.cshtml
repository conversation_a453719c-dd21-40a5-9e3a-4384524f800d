﻿@model Vehicle

<form id="vehicleForm">
    @Html.HiddenFor(m => m.SerialNumber)

    <fieldset id="form-fieldset">
        <div class="row">
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.SerialNumber)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.SerialNumber, new { @class = "form-control", @readonly = "readonly" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Brand)
                @Html.TextBoxFor(m => m.<PERSON>, new { @class = "form-control" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Model)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.Model, new { @class = "form-control" })
            </div>

            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Producttype)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.Producttype, new { @class = "form-control" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.ServiceEngineer)
                @Html.TextBoxFor(m => m.ServiceEngineer, new { @class = "form-control" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Operatorname)
                @Html.TextBoxFor(m => m.Operatorname, new { @class = "form-control" })
            </div>

            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.PrimarySegment)
                @Html.TextBoxFor(m => m.PrimarySegment, new { @class = "form-control" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Secondarysegment)
                @Html.TextBoxFor(m => m.Secondarysegment, new { @class = "form-control" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Machinestatus)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.Machinestatus, new { @class = "form-control" })
            </div>

            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.SaleszorderNumber)
                @Html.TextBoxFor(m => m.SaleszorderNumber, new { @class = "form-control" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Cuurentsiteaddress)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.Cuurentsiteaddress, new { @class = "form-control" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Remarks)
                @Html.TextBoxFor(m => m.Remarks, new { @class = "form-control" })
            </div>

            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.AverageHMR)
                @Html.TextBoxFor(m => m.AverageHMR, new { @class = "form-control", type = "number", step = "0.01" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.MachineHMR)
                @Html.TextBoxFor(m => m.MachineHMR, new { @class = "form-control", type = "number" })
            </div>
            <div class="col-md-4 form-group">
                @Html.LabelFor(m => m.Nextservicetype)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.Nextservicetype, new { @class = "form-control" })
            </div>

            <div class="col-md-3 form-group">
                @Html.LabelFor(m => m.SaleDate)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.SaleDate, "{0:yyyy-MM-dd}", new { @class = "form-control", type = "date" })
            </div>
            <div class="col-md-3 form-group">
                @Html.LabelFor(m => m.Commissioningdate)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.Commissioningdate, "{0:yyyy-MM-dd}", new { @class = "form-control", type = "date" })
            </div>
            <div class="col-md-3 form-group">
                @Html.LabelFor(m => m.Lastservicedate)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.Lastservicedate, "{0:yyyy-MM-dd}", new { @class = "form-control", type = "date" })
            </div>
            <div class="col-md-3 form-group">
                @Html.LabelFor(m => m.nextservicedate)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.nextservicedate, "{0:yyyy-MM-dd}", new { @class = "form-control", type = "date" })
            </div>

            <div class="col-md-3 form-group form-check mt-4">
                @Html.CheckBoxFor(m => m.Isactive, new { @class = "form-check-input" })
                @Html.LabelFor(m => m.Isactive, new { @class = "form-check-label" })
            </div>
            <div class="col-md-3 form-group form-check mt-4">
                @Html.CheckBoxFor(m => m.Isimported, new { @class = "form-check-input" })
                @Html.LabelFor(m => m.Isimported, new { @class = "form-check-label" })
            </div>
            <div class="col-md-3 form-group">
                @Html.LabelFor(m => m.LastHMRupdateddate)
                <span class="required-indicator">*</span>
                @Html.TextBoxFor(m => m.LastHMRupdateddate, "{0:yyyy-MM-dd}", new { @class = "form-control", type = "date" })
            </div>
        </div>
    </fieldset>

    <div class="mt-3">
        <button type="button" id="backBtn" class="action-btn" style="display:none;">Back</button>
        <button type="submit" id="saveBtn" class="action-btn" style="display:none;">Save Changes</button>
    </div>
</form>