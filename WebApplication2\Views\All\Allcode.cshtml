﻿@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Sample</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link href="~/Assets/css/styles.css" rel="stylesheet" />
</head>
<body>
    <div class="container mt-4">
        <h6>Create New User</h6>
        <form id="createUserForm" class="form-inline mb-4">
            <input type="text" id="newFirstName" name="FirstName" placeholder="First Name" class="form-control mr-2" required>
            <input type="text" id="newLastName" name="LastName" placeholder="Last Name" class="form-control mr-2" required>
            <input type="email" id="newEmail" name="Email" placeholder="Email" class="form-control mr-2" required>
            <input type="password" id="newPassword"name="PasswordHash" placeholder="Password" class="form-control mr-2" required>
            <button type="submit" class="btn btn-success">Create User</button>
        </form>
        <div id="statusMessage" class="mt-2"></div>
        <hr />

        <h6>Existing Users</h6>
        <div class="row mb-3 align-items-center">
            <div class="col-md-4">
                <input type="text" id="searchInput" class="form-control" placeholder="Search by name or email...">
            </div>
            <div class="col-md-4">
                <select id="userIdFilter" class="form-control">
                    <option value="">Filter by User ID...</option>
                </select>
            </div>
            <div class="col-md-4 text-right">
                <button class="btn btn-sm btn-secondary view-toggle-btn active" data-view="table">Table View</button>
                <button class="btn btn-sm btn-secondary view-toggle-btn" data-view="card">Card View</button>
            </div>
        </div>

        <div id="dataContainer"></div>

        <!-- Data Container for Table or Cards -->
        

        <!-- Edit Modal -->
        <div class="modal fade" id="editModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form id="editForm">
                        <div class="modal-header">
                            <h5 class="modal-title">Edit User</h5>
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <input type="hidden" id="editUserId" name="UserID">
                            <div class="form-group">
                                <label>First Name:</label>
                                <input type="text" id="editFirstName" name="FirstName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>Last Name:</label>
                                <input type="text" id="editLastName" name="LastName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label>Email:</label>
                                <input type="email" id="editEmail" name="Email" class="form-control" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="~/Assets/js/scripts.js"></script>
</body>
</html>