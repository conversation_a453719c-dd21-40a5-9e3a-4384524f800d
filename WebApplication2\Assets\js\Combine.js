﻿$(document).ready(function () {
    // --- CONFIGURATION & CACHED SELECTORS ---
    const dataContainer = $('#data-container');
    const formModal = $('#form-modal');
    const pagingContainer = $('#paging-container');
    const pageSize = 15; // You can change how many records to show per page here
    const gridHeader = $('.grid-header');
    let currentPage = 1;
    // -----------------------------------------
    // --- DATA LOADING & UI RENDERING ---
    // -----------------------------------------

    /**
     * Fetches a specific page of data from the server.
     * @param {number} pageNumber The page number to request.
     */
    function loadData(pageNumber) {
        currentPage = pageNumber;
        debugger
        $.ajax({
            url: '/VehicleProduct/GetAll',
            type: 'GET',
            data: { pageNumber: pageNumber, pageSize: pageSize },
            success: function (response) {
                renderData(response.Items);
                renderPaging(response.TotalCount, pageNumber);
            },
            error: function () {
                dataContainer.html('<p class="error-message">Error loading data. Please try again.</p>');
            }
        });
    }

    /**
     * Renders the grid of items on the main page.
     * @param {Array} items The array of item data to render.
     */
    function renderData(items) {
        dataContainer.empty();
        if (!items || items.length === 0) {
            dataContainer.html('<p style="padding: 15px;">No data available.</p>');
            return;
        }

        const currentViewClass = dataContainer.attr('class');

        $.each(items, function (index, item) {
            let itemHtml = '';

            // Call the correct builder function based on the active view
            if (currentViewClass.includes('card-view')) {
                itemHtml = buildCardHtml(item);
            } else if (currentViewClass.includes('list-view')) {
                itemHtml = buildListHtml(item);
            } else if (currentViewClass.includes('compact-view')) {
                itemHtml = buildCompactHtml(item);
            } else { // Default to Grid View
                itemHtml = buildGridHtml(item);
            }
            dataContainer.append(itemHtml);
        });
    }

    // --- ADD THESE NEW HELPER FUNCTIONS to your app.js file ---

    function buildGridHtml(item) {
        const statusClass = (item.Machinestatus || 'unknown').toLowerCase().replace(/\s+/g, '-');
        return `
        <div class="data-item" data-id="${item.SerialNumber}">
            <div class="item-header">${item.Brand || 'N/A'} - ${item.VehicleModel || 'N/A'}</div>
            <div class="item-field">${item.SerialNumber}</div>
            <div class="item-field"><span class="status status-${statusClass}">${item.Machinestatus || 'N/A'}</span></div>
            <div class="item-field">${item.ReferenceNumber || 'None'}</div>
            <div class="item-actions">
                <button class="btn-view">View</button>
                <button class="btn-edit">Edit</button>
                <button class="btn-delete">Delete</button>
            </div>
        </div>`;
    }

    function buildCardHtml(item) {
        const statusClass = (item.Machinestatus || 'unknown').toLowerCase().replace(/\s+/g, '-');
        return `
        <div class="vehicle-card" data-id="${item.SerialNumber}">
            <div class="card-header">
                <div class="card-title">
                    <h3>${item.Brand || 'Unknown Brand'}</h3>
                    <span>Serial #: ${item.SerialNumber}</span>
                </div>
                <div class="card-status status-${statusClass}">${item.Machinestatus || 'N/A'}</div>
            </div>
            <div class="card-body">
                <div class="detail-item"><strong>Model:</strong> ${item.VehicleModel || 'N/A'}</div>
                <div class="detail-item"><strong>Company:</strong> ${item.Company || 'N/A'}</div>
            </div>
            <div class="card-footer">
                <button class="btn-view action-btn-secondary">View</button>
                <button class="btn-edit action-btn-secondary">Edit</button>
                <button class="btn-delete action-btn-danger">Delete</button>
            </div>
        </div>`;
    }

    function buildListHtml(item) {
        const statusClass = (item.Machinestatus || 'unknown').toLowerCase().replace(/\s+/g, '-');
        const brandInitial = (item.Brand || '?').charAt(0);
        return `
        <div class="list-item" data-id="${item.SerialNumber}">
            <div class="list-avatar">${brandInitial}</div>
            <div class="list-content">
                <div class="item-header">${item.Brand || 'N/A'} - ${item.VehicleModel || 'N/A'}</div>
                <div class="item-field">${item.SerialNumber}</div>
            </div>
            <div class="list-status"><span class="status status-${statusClass}">${item.Machinestatus || 'N/A'}</span></div>
            <div class="item-actions">
                <button class="btn-view">View</button>
                <button class="btn-edit">Edit</button>
                <button class="btn-delete">Delete</button>
            </div>
        </div>`;
    }

    function buildCompactHtml(item) {
        const statusClass = (item.Machinestatus || 'unknown').toLowerCase().replace(/\s+/g, '-');
        return `
        <div class="compact-item" data-id="${item.SerialNumber}">
            <div class="compact-info">
                <span class="item-header">${item.SerialNumber}</span>
                <span>-</span>
                <span class="item-field">${item.Brand || 'N/A'}</span>
            </div>
            <div class="item-actions">
                <span class="status status-${statusClass}">${item.Machinestatus || 'N/A'}</span>
                <button class="btn-view">View</button>
                <button class="btn-edit">Edit</button>
            </div>
        </div>`;
    }

    /**
     * Renders the paging controls (Previous, Next, page numbers).
     * @param {number} totalCount The total number of records in the database.
     * @param {number} currentPage The currently displayed page.
     */
    function renderPaging(totalCount, currentPage) {
        pagingContainer.empty();
        const totalPages = Math.ceil(totalCount / pageSize);
        if (totalPages <= 1) return;

        let pagingHtml = '';

        // Previous Button
        pagingHtml += `<button class="paging-btn" data-page="${currentPage - 1}" ${currentPage === 1 ? 'disabled' : ''}>← Previous</button>`;

        // Page Number Logic
        const pageWindow = 2; // How many pages to show around the current page
        let startPage = Math.max(1, currentPage - pageWindow);
        let endPage = Math.min(totalPages, currentPage + pageWindow);

        if (startPage > 1) {
            pagingHtml += `<button class="paging-btn" data-page="1">1</button>`;
            if (startPage > 2) {
                pagingHtml += `<span class="paging-ellipsis">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            pagingHtml += `<button class="paging-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">${i}</button>`;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pagingHtml += `<span class="paging-ellipsis">...</span>`;
            }
            pagingHtml += `<button class="paging-btn" data-page="${totalPages}">${totalPages}</button>`;
        }

        // Next Button
        pagingHtml += `<button class="paging-btn" data-page="${currentPage + 1}" ${currentPage === totalPages ? 'disabled' : ''}>Next →</button>`;

        pagingContainer.html(pagingHtml);
    }
    $('.view-switcher').on('click', '.view-btn', function () {
        if ($(this).hasClass('active')) return;
        $('.view-switcher .view-btn').removeClass('active');
        $(this).addClass('active');
        const newViewClass = $(this).attr('id').replace('btn-', '') + '-view';
        dataContainer.removeClass('grid-view card-view list-view compact-view').addClass(newViewClass);
        if (newViewClass === 'grid-view') {
            gridHeader.css('display', 'grid');
        } else {
            gridHeader.hide();
        }
    });

    // -----------------------------------------
    // --- DYNAMIC MODAL BUILDER ---
    // -----------------------------------------

    /**
     * Fetches data and builds the complex modal for View or Edit mode.
     * @param {string} serialNumber The vehicle's serial number.
     * @param {boolean} isEditMode True for Edit mode, false for View mode.
     */
    function showDetailsModal(serialNumber, isEditMode) {
        $.ajax({
            url: '/VehicleProduct/GetVehicleDetails',
            type: 'GET',
            data: { id: serialNumber },
            success: function (response) {
                if (response && response.VehicleInfo) {
                    let formHtml = `
                        <div class="details-container vehicle-details-box">
                            <h4>Vehicle Details</h4>
                             <div class="form-group"><label>Serial Number</label><input type="text" id="VehicleInfo_SerialNumber" value="${response.VehicleInfo.SerialNumber || ''}" disabled></div>
                        <div class="form-group"><label>Brand</label><input type="text" id="VehicleInfo_Brand" value="${response.VehicleInfo.Brand || ''}"></div>
                        <div class="form-group"><label>Model</label><input type="text" id="VehicleInfo_Model_Vehicle" value="${response.VehicleInfo.Model_Vehicle || ''}"></div>
                        <div class="form-group"><label>Service Engineer</label><input type="text" id="VehicleInfo_ServiceEngineer" value="${response.VehicleInfo.ServiceEngineer || ''}"></div>
                        <div class="form-group"><label>Primary Segment</label><input type="text" id="VehicleInfo_PrimarySegment" value="${response.VehicleInfo.PrimarySegment || ''}"></div>
                        <div class="form-group"><label>Sale Date</label><input type="datetime-local" id="VehicleInfo_SaleDate" value="${formatDateForInput(response.VehicleInfo.SaleDate)}"></div>
                        <div class="form-group"><label>Average HMR</label><input type="number" id="VehicleInfo_AverageHMR" value="${response.VehicleInfo.AverageHMR || ''}"></div>
                        <div class="form-group"><label>Sales Order Number</label><input type="text" id="VehicleInfo_SaleszorderNumber" value="${response.VehicleInfo.SaleszorderNumber || ''}"></div>
                        <div class="form-group"><label>Next Service Type</label><input type="text" id="VehicleInfo_Nextservicetype" value="${response.VehicleInfo.Nextservicetype || ''}"></div>
                        <div class="form-group"><label>Last HMR Updated</label><input type="datetime-local" id="VehicleInfo_LastHMRupdateddate" value="${formatDateForInput(response.VehicleInfo.LastHMRupdateddate)}"></div>
                        <div class="form-group"><label>Remarks</label><input type="text" id="VehicleInfo_Remarks" value="${response.VehicleInfo.Remarks || ''}"></div>
                        <div class="form-group"><label>Operator Name</label><input type="text" id="VehicleInfo_Operatorname" value="${response.VehicleInfo.Operatorname || ''}"></div>
                        <div class="form-group"><label>Product Type</label><input type="text" id="VehicleInfo_Producttype" value="${response.VehicleInfo.Producttype || ''}"></div>
                        <div class="form-group"><label>Machine Status</label><input type="text" id="VehicleInfo_Machinestatus" value="${response.VehicleInfo.Machinestatus || ''}"></div>
                        <div class="form-group"><label>Secondary Segment</label><input type="text" id="VehicleInfo_Secondarysegment" value="${response.VehicleInfo.Secondarysegment || ''}"></div>
                        <div class="form-group"><label>Commissioning Date</label><input type="datetime-local" id="VehicleInfo_Commissioningdate" value="${formatDateForInput(response.VehicleInfo.Commissioningdate)}"></div>
                        <div class="form-group"><label>Machine HMR</label><input type="number" id="VehicleInfo_MachineHMR" value="${response.VehicleInfo.MachineHMR || ''}"></div>
                        <div class="form-group"><label>Last Service Date</label><input type="datetime-local" id="VehicleInfo_Lastservicedate" value="${formatDateForInput(response.VehicleInfo.Lastservicedate)}"></div>
                        <div class="form-group"><label>Current Site Address</label><input type="text" id="VehicleInfo_Cuurentsiteaddress" value="${response.VehicleInfo.Cuurentsiteaddress || ''}"></div>
                        <div class="form-group"><label>Next Service Date</label><input type="datetime-local" id="VehicleInfo_nextservicedate" value="${formatDateForInput(response.VehicleInfo.nextservicedate)}"></div>
                        <div class="form-group checkbox-group"><input type="checkbox" id="VehicleInfo_Isimported" ${response.VehicleInfo.Isimported ? 'checked' : ''}><label for="VehicleInfo_Isimported">Is Imported</label></div>
                        <div class="form-group checkbox-group"><input type="checkbox" id="VehicleInfo_Isactive" ${response.VehicleInfo.Isactive ? 'checked' : ''}><label for="VehicleInfo_Isactive">Is Active</label></div>
                    </div>
                    <div class="details-container readings-details-box">
                        <h4>Product Readings</h4>`;
                    if (response.Readings && response.Readings.length > 0) {
                        formHtml += '<table class="readings-table"><thead><tr><th>Ref #</th><th>Company</th><th>Mode</th><th>Date</th></tr></thead><tbody>';
                        response.Readings.forEach(function (reading, index) {
                            formHtml += `
                            <tr class="reading-item" data-index="${index}">
                                <td><input type="text" name="Readings[${index}].ReferenceNumber" value="${reading.ReferenceNumber || ''}" disabled></td>
                                <td><input type="text" name="Readings[${index}].Company" value="${reading.Company || ''}"></td>
                                <td><input type="text" name="Readings[${index}].Mode" value="${reading.Mode || ''}"></td>
                                <td><input type="datetime-local" name="Readings[${index}].ReferenceDate" value="${formatDateForInput(reading.ReferenceDate)}"></td>
                                <input type="hidden" name="Readings[${index}].SerialNumber" value="${reading.SerialNumber || ''}">
                            </tr>`;
                        });
                        formHtml += '</tbody></table>';
                    } else {
                        formHtml += '<p>No readings found for this vehicle.</p>';
                    }

                    formHtml += '</div>';
                    if (isEditMode) {
                        formHtml += `<div class="form-group full-width"><button type="submit" class="action-btn submit-btn">Save Changes</button></div>`;
                    }

                    $('#readingForm').html(formHtml);
                    const title = isEditMode ? 'Edit Vehicle & Readings' : 'View Vehicle Details';
                    $('#form-modal-title').text(title);
                    $('#readingForm input, #readingForm select').prop('disabled', !isEditMode);
                    $('#VehicleInfo_SerialNumber').prop('disabled', true); // Primary key is never editable
                    formModal.show();
                }
            }
        });
    }

    // -----------------------------------------
    // --- EVENT HANDLERS ---
    // -----------------------------------------

    // Handles clicks on the paging buttons
    pagingContainer.on('click', '.paging-btn', function () {
        const pageNumber = $(this).data('page');
        if (pageNumber) loadData(pageNumber);
    });

    // Handles the main "Create New" button
    $('#btn-create').on('click', function () {
        let formHtml = `
            <div class="details-container vehicle-details-box">
                <h4>New Vehicle Details</h4>
                <div class="form-group"><label>Serial Number</label><input type="text" id="VehicleInfo_SerialNumber"></div>
                <div class="form-group"><label>Brand</label><input type="text" id="VehicleInfo_Brand"></div>
                <div class="form-group"><label>Model</label><input type="text" id="VehicleInfo_Model_Vehicle"></div>
                <div class="form-group"><label>Service Engineer</label><input type="text" id="VehicleInfo_ServiceEngineer"></div>
                <div class="form-group"><label>Primary Segment</label><input type="text" id="VehicleInfo_PrimarySegment"></div>
                <div class="form-group"><label>Sale Date</label><input type="datetime-local" id="VehicleInfo_SaleDate"></div>
                <div class="form-group"><label>Average HMR</label><input type="number" id="VehicleInfo_AverageHMR"></div>
                <div class="form-group"><label>Sales Order Number</label><input type="text" id="VehicleInfo_SaleszorderNumber"></div>
                <div class="form-group"><label>Next Service Type</label><input type="text" id="VehicleInfo_Nextservicetype"></div>
                <div class="form-group"><label>Last HMR Updated</label><input type="datetime-local" id="VehicleInfo_LastHMRupdateddate"></div>
                <div class="form-group"><label>Remarks</label><input type="text" id="VehicleInfo_Remarks"></div>
                <div class="form-group"><label>Operator Name</label><input type="text" id="VehicleInfo_Operatorname"></div>
                <div class="form-group"><label>Product Type</label><input type="text" id="VehicleInfo_Producttype"></div>
                <div class="form-group"><label>Machine Status</label><input type="text" id="VehicleInfo_Machinestatus"></div>
                <div class="form-group"><label>Secondary Segment</label><input type="text" id="VehicleInfo_Secondarysegment"></div>
                <div class="form-group"><label>Commissioning Date</label><input type="datetime-local" id="VehicleInfo_Commissioningdate"></div>
                <div class="form-group"><label>Machine HMR</label><input type="number" id="VehicleInfo_MachineHMR"></div>
                <div class="form-group"><label>Last Service Date</label><input type="datetime-local" id="VehicleInfo_Lastservicedate"></div>
                <div class="form-group"><label>Current Site Address</label><input type="text" id="VehicleInfo_Cuurentsiteaddress"></div>
                <div class="form-group"><label>Next Service Date</label><input type="datetime-local" id="VehicleInfo_nextservicedate"></div>
                <div class="form-group checkbox-group">
                    <input type="checkbox" id="VehicleInfo_Isimported">
                    <label for="VehicleInfo_Isimported">Is Imported</label>
                </div>
                <div class="form-group checkbox-group">
                    <input type="checkbox" id="VehicleInfo_Isactive">
                    <label for="VehicleInfo_Isactive">Is Active</label>
                </div>
            </div>

            </div>
            <div class="details-container readings-details-box">
                <h4>First Product Reading</h4>
                <div class="reading-item" data-index="0">
                    <div class="form-group"><label>Reference Number</label><input type="text" name="Readings[0].ReferenceNumber"></div>
                    <div class="form-group"><label>Company</label><input type="text" name="Readings[0].Company"></div>
                    <div class="form-group"><label>Mode</label><input type="text" name="Readings[0].Mode"></div>
                    <div class="form-group"><label>Date</label><input type="datetime-local" name="Readings[0].ReferenceDate"></div>
                </div>
                 <div class="form-group full-width"><button type="submit" class="action-btn submit-btn">Create Record</button></div>
            </div>`
           
        $('#readingForm').html(formHtml);
        $('#form-modal-title').text('Create New Vehicle and Reading');
        formModal.show();
    });

    // Handles the modal's close button
    $('.close-btn').on('click', function () {
        formModal.hide(); $('#readingForm').html('');
    });

    // Handles the "View" button on a grid item
    dataContainer.on('click', '.btn-view', function () {
        debugger
        showDetailsModal($(this).closest('.data-item').data('id'), false);
    });

    // Handles the "Edit" button on a grid item
    dataContainer.on('click', '.btn-edit', function () {
        debugger
        showDetailsModal($(this).closest('.data-item').data('id'), true);
    });

    // Handles the "Delete" button on a grid item
    dataContainer.on('click', '.btn-delete', function () {
        debugger
        const serialId = $(this).closest('.data-item').data('id');
        if (confirm(`WARNING: This will permanently delete vehicle ${serialId} and ALL its readings. Are you sure?`)) {
            $.ajax({
               
                url: '/VehicleProduct/Delete',
                type: 'POST',
                data: { id: serialId },
                success: function (response) {
                    alert(response.message);
                    if (response.success) loadData(1);
                },
                error: function () { alert('An error occurred during deletion.'); }
            });
        }
    });

    // Handles the form submission for BOTH Create and Update
    $('#readingForm').on('submit', function (e) {
        e.preventDefault();
        const isUpdating = $('#VehicleInfo_SerialNumber').is(':disabled');
        const actionUrl = isUpdating ? '/VehicleProduct/Update' : '/VehicleProduct/Create';

        const payload = {
            VehicleInfo: {
                SerialNumber: $('#VehicleInfo_SerialNumber').val(),
                Brand: $('#VehicleInfo_Brand').val(),
                Model_Vehicle: $('#VehicleInfo_Model_Vehicle').val(),
                ServiceEngineer: $('#VehicleInfo_ServiceEngineer').val(),
                PrimarySegment: $('#VehicleInfo_PrimarySegment').val(),
                SaleDate: $('#VehicleInfo_SaleDate').val(),
                AverageHMR: $('#VehicleInfo_AverageHMR').val(),
                SaleszorderNumber: $('#VehicleInfo_SaleszorderNumber').val(),
                Nextservicetype: $('#VehicleInfo_Nextservicetype').val(),
                LastHMRupdateddate: $('#VehicleInfo_LastHMRupdateddate').val(),
                Remarks: $('#VehicleInfo_Remarks').val(),
                Operatorname: $('#VehicleInfo_Operatorname').val(),
                Producttype: $('#VehicleInfo_Producttype').val(),
                Machinestatus: $('#VehicleInfo_Machinestatus').val(),
                Secondarysegment: $('#VehicleInfo_Secondarysegment').val(),
                Commissioningdate: $('#VehicleInfo_Commissioningdate').val(),
                MachineHMR: $('#VehicleInfo_MachineHMR').val(),
                Lastservicedate: $('#VehicleInfo_Lastservicedate').val(),
                Cuurentsiteaddress: $('#VehicleInfo_Cuurentsiteaddress').val(),
                nextservicedate: $('#VehicleInfo_nextservicedate').val(),
                Isimported: $('#VehicleInfo_Isimported').is(':checked'),
                Isactive: $('#VehicleInfo_Isactive').is(':checked')
            },
            Readings: []
        };

        $('.reading-item').each(function () {
            const index = $(this).data('index');
            const serialNumberForReading = isUpdating ? payload.VehicleInfo.SerialNumber : $(`#VehicleInfo_SerialNumber`).val();
            payload.Readings.push({
                ReferenceNumber: $(`input[name="Readings[${index}].ReferenceNumber"]`).val(),
                Company: $(`input[name="Readings[${index}].Company"]`).val(),
                Mode: $(`input[name="Readings[${index}].Mode"]`).val(),
                ReferenceDate: $(`input[name="Readings[${index}].ReferenceDate"]`).val(),
                SerialNumber: serialNumberForReading
            });
        });
        debugger
        $.ajax({
            url: actionUrl,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(payload),
            success: function (response) {
                alert(response.message);
                if (response.success) {
                    formModal.hide();
                    loadData(currentPage);
                }
            },
            error: function () { alert('An error occurred while saving.'); }
        });
    });

    // -----------------------------------------
    // --- HELPER FUNCTIONS ---
    // -----------------------------------------
    function formatDateForInput(dateString) {
        if (!dateString) return '';
        if (dateString.includes('/Date(')) {
            const timestamp = parseInt(dateString.match(/-?\d+/)[0]);
            if (isNaN(timestamp)) return '';
            return new Date(timestamp).toISOString().slice(0, 16);
        }
        const date = new Date(dateString);
        if (isNaN(date)) return '';
        return date.toISOString().slice(0, 16);
    }

    // --- INITIAL DATA LOAD ---
    loadData(1);
});