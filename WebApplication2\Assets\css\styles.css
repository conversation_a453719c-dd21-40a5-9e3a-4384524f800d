﻿body {
    overflow: auto;
}
#hiddenText {
    display: none;
    font-size: 18px;
    color: #333;
    margin-top: 10px;
}
/* Basic table styling */
.styled-table {
    border-collapse: collapse; /* Merges cell borders */
    margin: 25px 0;
    font-size: 0.9em;
    font-family: sans-serif;
    min-width: 400px;
    width: 130%; /* Make the table take up the full width */
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

    /* Style for the table header */
    .styled-table thead tr {
        background-color: #2EC0CB; 
        color: #ffffff;
        text-align: left;
    }

    /* Padding for all cells to add space */
    .styled-table th,
    .styled-table td {
        padding: 12px 15px;
    }

    /* Style for the table body rows */
    .styled-table tbody tr {
        border-bottom: 1px solid #dddddd;
    }

        /* Alternating row colors (zebra-striping) */
        .styled-table tbody tr:nth-of-type(even) {
            background-color: #f3f3f3;
        }

        /* Final border at the bottom of the table */
        .styled-table tbody tr:last-of-type {
            border-bottom: 2px solid #009879;
        }

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: blanchedalmond;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 500px;
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}
:root {
    --primary-color: #000000;
    --success-color: #28a745;
    --warning-color: #fd7e14;
    --danger-color: #dc3545;
    --dark-color: #343a40;
    --purple-color: #6f42c1;
    --light-gray-bg: #f8f9fa;
    --border-color: #e0e0e0;
    --text-dark: #333;
    --text-light: #666;
    --white: #fff;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    --off-white-bg: #fafafa;
    --border-light: #ddd;
    --accent-blue: #007bff;
    --backdrop-color: rgba(0, 0, 0, 0.5);
    --border-extra-light: #e9e9e9;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Cambria;
}

body {
    font-family: Cambria !important;
    background-color: var(--white);
    color: var(--primary-color);
}

.CW-main-content {
    overflow-y: scroll;
    height: 785px;
}

.CW-container {
    margin: 20px auto;
    background: var(--white);
    padding-left: 20px;
    padding-right: 20px;
}

/* Header & Toolbar */
.CW-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid var(--border-color);
}

    .CW-header h1 {
        font-size: 24px;
    }

.CW-top-header-bar,
.CW-bottom-header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    gap: 20px;
}

.CW-top-header-bar {
    border-bottom: 1px solid var(--border-color);
}

.CW-bottom-header-bar {
    background: var(--off-white-bg);
    border-bottom: 1px solid var(--border-color);
}

.CW-top-right-controls,
.CW-bottom-left-controls,
.CW-bottom-right-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Stats Cards */
.CW-stats-container {
    display: flex;
    gap: 5px;
}

.CW-stat-card {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    background-color: var(--light-gray-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.CW-stat-icon {
    font-size: 20px;
}

    .CW-stat-icon.CW-completed {
        color: var(--success-color);
    }

    .CW-stat-icon.CW-pending {
        color: var(--warning-color);
    }

    .CW-stat-icon.CW-rejected {
        color: var(--danger-color);
    }

.CW-stat-value {
    font-size: 18px;
    font-weight: 700;
}

.CW-stat-label {
    font-size: 11px;
    color: var(--text-light);
}

/* Search Container */
.CW-search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.CW-search-input {
    padding: 10px 12px 10px 35px;
    border-radius: 6px;
    border: 1px solid var(--border-extra-light);
}

.CW-search-icon {
    position: absolute;
    left: 12px;
    color: var(--text-light);
}

/* General Buttons */
.CW-btn {
    padding: 10px 10px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
}

.CW-btn-success {
    background: var(--success-color);
    color: var(--white);
}

/* View Toggles */
.CW-view-controls {
    display: flex;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    overflow: hidden;
}

.CW-view-btn {
    padding: 10px 16px;
    border: none;
    background: var(--white);
    cursor: pointer;
    border-right: 1px solid var(--border-light);
}

    .CW-view-btn:last-child {
        border-right: none;
    }

    .CW-view-btn.CW-active {
        background: var(--primary-color);
        color: var(--white);
    }

/* Data Display */
.CW-data-container {
    height: 600px;
    overflow-y: auto;
}

.CW-view-content {
    display: none;
}

    .CW-view-content.CW-active {
        display: block;
    }

/* Table / Grid View */
.CW-table-container {
    overflow-x: auto;
}

.CW-data-table {
    width: 100%;
    border-collapse: collapse;
}

    .CW-data-table th, .CW-data-table td {
        padding: 8px 30px;
        border-bottom: 1px solid var(--border-color);
    }

    .CW-data-table th {
        background: var(--primary-color);
        color: var(--white);
    }

    .CW-data-table .CW-search-row th {
        background-color: var(--light-hover-bg);
    }

.CW-actions-col {
    text-align: center;
}

/* Card View */
.CW-cards-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.CW-vehicle-card-new {
    border: 1px solid var(--border-extra-light);
    border-radius: var(--border-radius);
    padding: 20px;
}

/* List View */
.CW-list-view-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.CW-list-view-item-new {
    display: grid;
    grid-template-columns: 3fr 2.5fr 2.5fr 2fr 1.5fr;
    gap: 20px;
    align-items: center;
    border: 1px solid var(--border-extra-light);
    border-radius: var(--border-radius);
    padding: 15px 20px;
}

/* Pagination */
.CW-pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-top: 1px solid var(--border-color);
}

.CW-pagination-info, .CW-pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.CW-pagination-btn, .CW-page-number {
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    background: var(--white);
    cursor: pointer;
}

    .CW-page-number.CW-active {
        background: var(--primary-color);
        color: var(--white);
    }
.card-view-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.lead-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 15px;
    border-left: 5px solid #6c757d; /* Default border color */
}

    .lead-card.status-draft {
        border-left-color: #6c757d;
    }

    .lead-card.status-contacted {
        border-left-color: #007bff;
    }

    .lead-card.status-qualified {
        border-left-color: #28a745;
    }

    .lead-card.status-lost {
        border-left-color: #dc3545;
    }

.card-header {
    display: flex;
    justify-content: space-between;
    font-size: 1.1em;
    font-weight: bold;
    margin-bottom: 15px;
}

.card-status {
    font-size: 0.9em;
    font-weight: normal;
    text-transform: capitalize;
}

.card-body {
    font-size: 0.9em;
    margin-bottom: 15px;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #f0f0f0;
    padding-top: 10px;
}

.card-date {
    font-size: 0.8em;
    color: #666;
}

.legend-btn {
    margin-right: 5px;
    margin-bottom: 5px;
}
/* Main container for the cards */
/* Container for the cards, uses a responsive grid */
.user-card-component {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); /* A slightly wider minimum for better spacing */
    gap: 20px;
}

    /* Individual User Card */
    .user-card-component .card {
        background-color: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        display: flex;
        flex-direction: column;
        transition: box-shadow 0.3s ease, transform 0.3s ease;
    }

        .user-card-component .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

    /* Card Header for the Name */
    .user-card-component .card-header {
        padding: 15px;
        font-size: 1.2em;
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        word-break: break-word; /* Fixes long names breaking layout */
    }

    /* Card Body for Details */
    .user-card-component .card-body {
        padding: 15px;
        flex-grow: 3; /* Allows the body to take up available space */
    }

    .user-card-component .card-email {
        color: #555;
        margin-bottom: 10px;
        word-break: break-all; /* Fixes long emails breaking layout */
    }

    /* Card Footer for Date and Actions */
    .user-card-component .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #e0e0e0;
        border-radius: 0 0 8px 8px;
    }

    .user-card-component .card-date {
        font-size: 0.8em;
        color: #6c757d;
    }

    /* Container for the action buttons */
    .user-card-component .card-actions {
        display: flex;
        gap: 8px; /* Adds space between buttons */
    }

        .user-card-component .card-actions .btn {
            padding: 5px 12px;
            font-size: 0.9em;
            border: none;
            color: white;
            border-radius: 5px;
            cursor: pointer;
        }

    .user-card-component .edit-btn {
        background-color: #007bff; /* Blue */
    }

    .user-card-component .delete-btn {
        background-color: #dc3545; /* Red */
    }