$(function () {

    // --- MOCK DATA FOR UI DEMO ---
    const mockWarehouses = [
        { "ID": 1, "Name": "177N-WARR", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 2, "Name": "177N-PROJ", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 1, "Name": "177N-WARR", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 1, "Name": "177N-WARR", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 1, "Name": "177N-WARR", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 1, "Name": "177N-WARR", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 1, "Name": "177N-WARR", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 1, "Name": "177N-WARR", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 1, "Name": "177N-WARR", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" },
        { "ID": 3, "Name": "177N-MB03", "IsDefault": "No", "IsActive": "Yes", "IsVisible": "Yes" }
    ];

    // --- CORE FUNCTIONS ---
    function loadData() {
        const tbody = $('#data-table-body');
        tbody.empty();

        mockWarehouses.forEach(function (item) {
            const row = `
                <tr data-id="${item.ID}">
                    <td class="CW-checkbox-col"><input type="checkbox"></td>
                    <td>${item.Name}</td>
                    <td>${item.IsDefault}</td>
                    <td>${item.IsActive}</td>
                    <td>${item.IsVisible}</td>
                    <td class="CW-actions-col">
                        <button class="CW-btn edit-btn">Edit</button>
                        <button class="CW-btn delete-btn">Delete</button>
                    </td>
                </tr>`;
            tbody.append(row);
        });
    }

    // --- EVENT HANDLERS ---

    // CREATE: Handle "Add New" button click
    $("#addBtn").on("click", function () {
        const newRow = `
            <tr class="inline-add-row">
                <td class="CW-checkbox-col"></td>
                <td><input type="text" class="form-control" name="Name" placeholder="Name"></td>
                <td><input type="text" class="form-control" name="IsDefault" placeholder="Yes/No"></td>
                <td><input type="text" class="form-control" name="IsActive" placeholder="Yes/No"></td>
                <td><input type="text" class="form-control" name="IsVisible" placeholder="Yes/No"></td>
                <td class="CW-actions-col">
                    <button class="btn btn-sm btn-success save-new-btn">Save</button>
                    <button class="btn btn-sm btn-warning cancel-add-btn">Cancel</button>
                </td>
            </tr>`;
        $('#data-table-body').prepend(newRow);
    });

    // SAVE a NEW row
    $(document).on("click", ".save-new-btn", function () {
        alert("Simulating SAVE NEW record. In a real app, this would make an AJAX POST call to your create action.");
        loadData(); // Refresh table
    });

    // CANCEL a NEW row
    $(document).on("click", ".cancel-add-btn", function () {
        $(this).closest("tr").remove();
    });

    // EDIT an existing row
    $(document).on("click", ".edit-btn", function () {
        const row = $(this).closest("tr");

        row.find("td:not(.CW-checkbox-col, .CW-actions-col)").each(function () {
            const cell = $(this);
            const originalText = cell.text();
            cell.html(`<input type="text" class="form-control" value="${originalText}">`);
        });

        row.find(".CW-actions-col").html(`
            <button class="btn btn-sm btn-success save-update-btn">Save</button>
            <button class="btn btn-sm btn-warning cancel-edit-btn">Cancel</button>
        `);
    });

    // SAVE an UPDATED row
    $(document).on("click", ".save-update-btn", function () {
        const row = $(this).closest("tr");
        const id = row.data("id");
        alert(`Simulating UPDATE for record ID: ${id}. In a real app, this would make an AJAX POST call to your update action.`);
        loadData(); // Refresh table
    });

    // CANCEL an edit
    $(document).on("click", ".cancel-edit-btn", function () {
        loadData(); // Just refresh the table to discard changes
    });

    // DELETE a row
    $(document).on("click", ".delete-btn", function () {
        const row = $(this).closest("tr");
        const id = row.data("id");
        if (confirm(`Are you sure you want to delete record ${id}?`)) {
            alert(`Simulating DELETE for record ID: ${id}. In a real app, this would make an AJAX POST call to your delete action.`);
            loadData(); // Refresh table
        }
    });

    // Initial load
    loadData();
});