﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;

namespace WebApplication2.Models
{
    public class common : Controller
    {
        public static string _LogFilePath = "C:\\LogSheetExport";
        public static string _LogFileFullPath = "C:\\LogSheetExport\\ErrorLog.txt";

        public static void LogToTextFile(int ExId, string ExMessage, string ExDetails, string ExStackTrace)
        {
            try
            {

                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendFormat("Exception Date: {0}", DateTime.Now);
                stringBuilder.AppendFormat("{0}Exception From:", Environment.NewLine);
                stringBuilder.AppendFormat("{0}{1}: {2}", Environment.NewLine, ExMessage, ExId);
                stringBuilder.AppendFormat("{0}{0}Exception Information{0}{1}", Environment.NewLine, ExDetails);
                stringBuilder.AppendFormat("{0}{0}Stack Trace{0}{1}", Environment.NewLine, ExStackTrace);
                if (!Directory.Exists(_LogFilePath))
                {
                    Directory.CreateDirectory(_LogFilePath);
                }

                using (StreamWriter streamWriter = System.IO.File.AppendText(_LogFileFullPath))
                {
                    streamWriter.WriteLine(stringBuilder.ToString());
                    streamWriter.WriteLine("----------------------------------");
                }
            }
            catch (Exception)
            {
            }
        }
    }
}