$(function () {
    // --- STATE MANAGEMENT ---
    let allVehicles = []; // Holds the summary data for the list
    let selectedVehicleSerial = null; // Tracks the currently selected vehicle
    let currentPage = 1;
    const itemsPerPage = 10; // Adjust how many items you want per page
    let currentDetailView = 'form';

    //function validateForm() {
    //    let isValid = true;
    //    // Remove previous error styles
    //    $('#vehicleForm .input-validation-error').removeClass('input-validation-error');

    //    // Check each required field
    //    $('#vehicleForm [required]').each(function () {
    //        if ($(this).val().trim() === '') {
    //            $(this).addClass('input-validation-error');
    //            isValid = false;
    //        }
    //    });

    //    return isValid;
    //}

    function showToast(message, type = 'success') {
        const toast = $(`<div class="toast ${type}">${message}</div>`);
        $('#toastContainer').append(toast);
        setTimeout(() => toast.addClass('show'), 100);
        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => toast.remove(), 500);
        }, 3000);
    }

    // NEW: Converts Microsoft's /Date(ticks)/ format to YYYY-MM-DD for date inputs
    function formatDateForInput(msDate) {
        if (!msDate) return '';
        const date = new Date(parseInt(msDate.substr(6)));
        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        return `${year}-${month}-${day}`;
    }
    //date validations 
    // This single function handles both required fields and date logic
    function validateForm() {
        let isValid = true;
        // Clear previous errors from the form
        $('.validation-error').remove();
        $('#vehicleForm .input-validation-error').removeClass('input-validation-error');

        // --- STEP 1: Check that all mandatory fields are filled out ---
        $('#vehicleForm [required]').each(function () {
            if ($(this).val().trim() === '') {
                $(this).addClass('input-validation-error');
                $(this).after('<div class="validation-error">This field is required.</div>');
                isValid = false;
            }
        });

        // If a required field is empty, stop now to prevent errors in the date checks
        if (!isValid) {
            return false;
        }

        // --- STEP 2: If required fields are okay, then check the date logic ---
        const saleDate = new Date($('#SaleDate').val());
        const commissioningDate = new Date($('#Commissioningdate').val());
        const lastServiceDate = new Date($('#Lastservicedate').val());
        const nextServiceDate = new Date($('#nextservicedate').val());

        if (commissioningDate < saleDate) {
            $('#Commissioningdate').addClass('input-validation-error').after('<div class="validation-error">Must be on or after Sale Date.</div>');
            isValid = false;
        }
        if (lastServiceDate < saleDate) {
            $('#Lastservicedate').addClass('input-validation-error').after('<div class="validation-error">Must be on or after Sale Date.</div>');
            isValid = false;
        }
        if (nextServiceDate < lastServiceDate) {
            $('#nextservicedate').addClass('input-validation-error').after('<div class="validation-error">Cannot be before Last Service Date.</div>');
            isValid = false;
        }

        return isValid;
    }

    // Ensure your form's submit handler calls this new, single function
    $(document).on('submit', '#vehicleForm', function (e) {
        e.preventDefault();

        if (!validateForm()) {
            showToast("Please correct the errors before saving.", "error");
            return; // Stop if validation fails
        }

        // ... your existing AJAX call to save the data ...
    });
    // --- DATA LOADING & RENDERING ---

    function loadVehicleList() {
        $.ajax({
            url: '/Vehicle/GetVehicleSummaryList',
            type: 'GET',
            success: function (vehicles) {
                allVehicles = vehicles;
                renderPage();
                //applyFiltersAndRender(); // Render based on current filters (like search)

                if (vehicles.length > 0 && !selectedVehicleSerial) {
                    const firstSerial = vehicles[0].SerialNumber;
                    $('#vehicleList .list-item:first').addClass('active');
                    loadDetailView(firstSerial, false);
                } else if (vehicles.length === 0) {
                    $('#detailViewContainer').html('<p class="placeholder-text">No vehicles found. Click the "+" button to add one.</p>');
                    $('#editBtn, #deleteBtn').prop('disabled', true);
                }
            }
        });
    }
    function getFilteredData() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        if (searchTerm) {
            return allVehicles.filter(v =>
                (v.SerialNumber && v.SerialNumber.toLowerCase().includes(searchTerm)) ||
                (v.Model && v.Model.toLowerCase().includes(searchTerm)) ||
                (v.Producttype && v.Producttype.toLowerCase().includes(searchTerm)) ||
                (v.Brand && v.Brand.toLowerCase().includes(searchTerm)) ||
                (v.ServiceEngineer && v.ServiceEngineer.toLowerCase().includes(searchTerm)) ||
                (v.PrimarySegment && v.PrimarySegment.toLowerCase().includes(searchTerm)) ||
                (v.Secondarysegment && v.Secondarysegment.toLowerCase().includes(searchTerm)) ||
                (v.Operatorname && v.Operatorname.toLowerCase().includes(searchTerm)) ||
                (v.Machinestatus && v.Machinestatus.toLowerCase().includes(searchTerm)) ||
                (v.Cuurentsiteaddress && v.Cuurentsiteaddress.toLowerCase().includes(searchTerm)) ||
                (v.Remarks && v.Remarks.toLowerCase().includes(searchTerm)) ||
                (v.SaleszorderNumber && v.SaleszorderNumber.toLowerCase().includes(searchTerm)) ||
                (v.Nextservicetype && v.Nextservicetype.toLowerCase().includes(searchTerm)) ||
                (v.LastHMRupdateddate && new Date(parseInt(v.LastHMRupdateddate.substr(6))).toLocaleDateString().includes(searchTerm)) ||
                (v.Commissioningdate && new Date(parseInt(v.Commissioningdate.substr(6))).toLocaleDateString().includes(searchTerm)) ||
                (v.SaleDate && new Date(parseInt(v.SaleDate.substr(6))).toLocaleDateString().includes(searchTerm)) ||
                (v.Lastservicedate && new Date(parseInt(v.Lastservicedate.substr(6))).toLocaleDateString().includes(searchTerm)) ||
                (v.nextservicedate && new Date(parseInt(v.nextservicedate.substr(6))).toLocaleDateString().includes(searchTerm))
            );
        }
        return allVehicles;
    }
    function renderPage() {
        const filteredVehicles = getFilteredData();
        renderVehicleList(filteredVehicles);
    }



    function renderVehicleList(vehicles) {
        const listBody = $('#vehicleList');
        listBody.empty();

        // --- PAGINATION LOGIC ---
        const totalPages = Math.ceil(vehicles.length / itemsPerPage);
        const startIndex = (currentPage - 1) * itemsPerPage;
        const paginatedVehicles = vehicles.slice(startIndex, startIndex + itemsPerPage);

        if (paginatedVehicles.length === 0) {
            listBody.html("<p style='padding: 15px;'>No vehicles found.</p>");
        }
        else {
            paginatedVehicles.forEach(v => {
                const statusIndicator = v.Isactive ? '<span class="status-indicator status-active"></span>' : '<span class="status-indicator status-inactive"></span>';
                const nextServiceDate = new Date(parseInt(v.nextservicedate.substr(6))).toLocaleDateString();
                const listItem = `
                <div class="list-item" data-serial="${v.SerialNumber}">
                    <input type="checkbox" class="vehicle-checkbox" data-serial="${v.SerialNumber}">
                    <div class="list-item-content">
                        <div class="list-item-header"><span>${v.SerialNumber}</span><span>Model: ${v.Model}</span></div>
                        <div class="list-item-footer"><span>Product type: ${v.Producttype}</span><span>Active: ${statusIndicator}</span></div>
                        <span>Next service date: ${nextServiceDate}</span>
       
                    </div>
                </div>`
                listBody.append(listItem);
            });
        }
        renderPagination(totalPages, vehicles.length);
        toggleDeleteButtonState();
    }
    function renderPagination(totalPages, totalItems) {
        const container = $('#paginationContainer');
        container.empty();
        if (totalItems === 0) {
            container.text('No records found.');
            return;
        }

        // This HTML creates the new layout with the input box
        const paginationHtml = `
        <div class="pagination-info">
            <span>View ${((currentPage - 1) * itemsPerPage) + 1} - ${Math.min(currentPage * itemsPerPage, totalItems)} of ${totalItems}</span>
        </div>
        <div class="pagination-controls">
            <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} data-page="1">
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} data-page="${currentPage - 1}">
                <i class="fas fa-angle-left"></i>
            </button>
            <span class="pagination-text">Page</span>
            <input type="number" class="page-input" value="${currentPage}" min="1" max="${totalPages}">
            <span class="pagination-text">of ${totalPages}</span>
            <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} data-page="${currentPage + 1}">
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} data-page="${totalPages}">
                <i class="fas fa-angle-double-right"></i>
            </button>
        </div>`;
        container.html(paginationHtml);
    }
    function loadDetailView(serialNumber, isEditMode) {
        selectedVehicleSerial = serialNumber;
        const detailContainer = $('#detailViewContainer');
        //detailContainer.html('<p>Loading...</p>');
        $('#editBtn').prop('disabled', false);
        $('#deleteBtn').prop('disabled', false);
        debugger
        $.ajax({
            url: '/Vehicle/GetVehicleDetails',
            type: 'GET',
            data: { serialNumber: serialNumber },
            success: function (vehicle) {
                if (!vehicle) {
                    detailContainer.html('<p>Could not load details.</p>'); return;
                }
                // FORM HTML 
                const formHtml = `
                    <form id="vehicleForm" class="vehicle-form-grid">
                        <div class="form-group">
            <label>Serial Number</label>
            <input type="text" name="SerialNumber" value="${vehicle.SerialNumber || ''}" >
        </div>
        <div class="form-group">
            <label>Brand</label>
            <input type="text" name="Brand" value="${vehicle.Brand || ''}">
        </div>
        <div class="form-group">
            <label>Model</label>
            <input type="text" name="Model" value="${vehicle.Model || ''}">
        </div>
        <div class="form-group">
            <label>Service Engineer</label>
            <input type="text" name="ServiceEngineer" value="${vehicle.ServiceEngineer || ''}">
        </div>
        <div class="form-group">
            <label>Primary Segment</label>
            <input type="text" name="PrimarySegment" value="${vehicle.PrimarySegment || ''}">
        </div>
        <div class="form-group">
            <label>Sale Date</label>
            <input type="${isEditMode ? 'date' : 'text'}" name="SaleDate" value="${isEditMode ? formatDateForInput(vehicle.SaleDate) : new Date(parseInt(vehicle.SaleDate.substr(6))).toLocaleDateString()}">
        </div>
        <div class="form-group">
            <label>Average HMR</label>
            <input type="number" step="0.01" name="AverageHMR" value="${vehicle.AverageHMR || ''}">
        </div>
        <div class="form-group">
            <label>Sales Order Number</label>
            <input type="text" name="SaleszorderNumber" value="${vehicle.SaleszorderNumber || ''}">
        </div>
        <div class="form-group">
            <label>Next Service Type</label>
            <input type="text" name="Nextservicetype" value="${vehicle.Nextservicetype || ''}">
        </div>
        <div class="form-group">
            <label>Last HMR Updated Date</label>
            <input type="${isEditMode ? 'date' : 'text'}" name="LastHMRupdateddate" value="${isEditMode ? formatDateForInput(vehicle.LastHMRupdateddate) : new Date(parseInt(vehicle.LastHMRupdateddate.substr(6))).toLocaleDateString()}">
        </div>
        <div class="form-group">
            <label>Remarks</label>
            <input type="text" name="Remarks" value="${vehicle.Remarks || ''}">
        </div>
        <div class="form-group">
            <label>Operator Name</label>
            <input type="text" name="Operatorname" value="${vehicle.Operatorname || ''}">
        </div>
        <div class="form-group">
            <label>Product Type</label>
            <input type="text" name="Producttype" value="${vehicle.Producttype || ''}">
        </div>
        <div class="form-group">
            <label>Machine Status</label>
            <input type="text" name="Machinestatus" value="${vehicle.Machinestatus || ''}">
        </div>
        <div class="form-group">
            <label>Secondary Segment</label>
            <input type="text" name="Secondarysegment" value="${vehicle.Secondarysegment || ''}">
        </div>
        <div class="form-group">
            <label>Commissioning Date</label>
            <input type="${isEditMode ? 'date' : 'text'}" name="Commissioningdate" value="${isEditMode ? formatDateForInput(vehicle.Commissioningdate) : new Date(parseInt(vehicle.Commissioningdate.substr(6))).toLocaleDateString()}">
        </div>
        <div class="form-group">
            <label>Machine HMR</label>
            <input type="number" name="MachineHMR" value="${vehicle.MachineHMR || ''}">
        </div>
        <div class="form-group">
            <label>Last Service Date</label>
            <input type="${isEditMode ? 'date' : 'text'}" name="Lastservicedate" value="${isEditMode ? formatDateForInput(vehicle.Lastservicedate) : new Date(parseInt(vehicle.Lastservicedate.substr(6))).toLocaleDateString()}">
        </div>
        <div class="form-group">
            <label>Current Site Address</label>
            <input type="text" name="Cuurentsiteaddress" value="${vehicle.Cuurentsiteaddress || ''}">
        </div>
        <div class="form-group">
            <label>Next Service Date</label>
            <input type="${isEditMode ? 'date' : 'text'}" name="nextservicedate" value="${isEditMode ? formatDateForInput(vehicle.nextservicedate) : new Date(parseInt(vehicle.nextservicedate.substr(6))).toLocaleDateString()}">
        </div>
        <div class="form-group">
            <label>Is Imported</label>
            <input type="checkbox" name="Isimported" value="true" ${vehicle.Isimported ? 'checked' : ''}>
        </div>
         <div class="form-group">
     <label>Is Active</label>
     <input type="checkbox" name="Isactive" value="true" ${vehicle.Isactive ? 'checked' : ''}>
 </div>
         <div id="form-actions" class="mt-3">
                        <button id="backBtn" class="action-btn" style="display:none;">Back</button>
                        <button id="saveBtn" class="action-btn" style="display:none;">Save Changes</button>
                    </div>
                        </form >`;
                detailContainer.html(formHtml);
                if (vehicle.WarrantyStatus) {
                    const warrantyHtml = `<div id="warrantyStatusContainer" class="alert alert-info" style="margin-bottom: 15px;">${vehicle.WarrantyStatus}</div>`;
                    $('#vehicleForm').before(warrantyHtml);
                }
                // This disables all form controls including checkboxes
                $('#vehicleForm').wrap('<fieldset id="form-fieldset"></fieldset>');
                $('#form-fieldset').prop('disabled', !isEditMode);

                if (isEditMode) { $('#saveBtn, #backBtn').show(); }
            }
        });
    }

    // NEW: Renders the 22 fields as a two-column table in the right panel
    function renderDetailTableView(vehicle) {
        const detailContainer = $('#detailViewContainer');
        let tableHtml = '<table class="detail-table">';
        for (const key in vehicle) {
            if (vehicle.hasOwnProperty(key)) {
                let value = vehicle[key];
                if (typeof value === 'string' && value.startsWith('/Date(')) {
                    value = new Date(parseInt(value.substr(6))).toLocaleDateString();
                }
                tableHtml += `<tr><th>${key}</th><td>${value}</td></tr>`;
            }
        }
        tableHtml += '</table>';
        detailContainer.html(tableHtml);
    }

    // NEW: Renders the 22 fields as a card in the right panel
    function renderDetailCardView(vehicle) {
        const detailContainer = $('#detailViewContainer');
        let cardHtml = '<div class="detail-card"><h4>Vehicle Details</h4><div class="data-grid">';
        for (const key in vehicle) {
            if (vehicle.hasOwnProperty(key)) {
                let value = vehicle[key];
                if (typeof value === 'string' && value.startsWith('/Date(')) {
                    value = new Date(parseInt(value.substr(6))).toLocaleDateString();
                }
                cardHtml += `
                    <div class="data-item">
                        <span class="data-label">${key}</span>
                        <span class="data-value">${value}</span>
                    </div>`;
            }
        }
        cardHtml += '</div></div>';
        detailContainer.html(cardHtml);
    }
    // --- EVENT HANDLERS ---




    // NEW: View Toggle Buttons
    $('.view-toggles').on('click', '.view-btn', function () {
        $('.view-toggles .view-btn').removeClass('active');
        $(this).addClass('active');
        currentDetailView = $(this).data('view');
        // Re-render the detail view if a vehicle is currently selected
        if (selectedVehicleSerial) {
            loadDetailView(selectedVehicleSerial, false); // Always switch back to read-only mode
        }
    });
    // PAGINATION
    $('#paginationContainer').on('click', '.pagination-btn', function () {
        const newPage = parseInt($(this).data('page'));
        if (newPage >= 1 && newPage <= Math.ceil(allVehicles.length / itemsPerPage)) {
            currentPage = newPage;
            //applyFiltersAndRender();
            renderPage();
        }
    });
    $('#paginationContainer').on('keypress', '.page-input', function (e) {
        if (e.which === 13) { // 13 is the keycode for the Enter key
            const newPage = parseInt($(this).val());
            const totalPages = Math.ceil(allVehicles.length / itemsPerPage);
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                renderPage();
            } else {
                alert('Invalid page number.');
                $(this).val(currentPage); // Revert to the current page if input is invalid
            }
        }
    });

    // --- CHECKBOX & Multi-DELETE LOGIC ---
    function toggleDeleteButtonState() {
        const anyChecked = $('#vehicleList .vehicle-checkbox:checked').length > 0;
        $('#deleteSelectedBtn').prop('disabled', !anyChecked);
    }
    $('#selectAllCheckbox').on('change', function () {
        $('#vehicleList .vehicle-checkbox').prop('checked', $(this).prop('checked'));
        toggleDeleteButtonState();
    });
    $(document).on('change', '.vehicle-checkbox', toggleDeleteButtonState);
    // --- EVENT HANDLERS ---

    //function applyFiltersAndRender() {
    //    debugger
    //    let filteredVehicles = allVehicles;
    //    // Search
    //    const searchTerm = $('#searchInput').val().toLowerCase();
    //    if (searchTerm) {
    //        filteredVehicles = allVehicles.filter(v =>
    //            (v.SerialNumber && v.SerialNumber.toLowerCase().includes(searchTerm)) ||
    //            (v.Model && v.Model.toLowerCase().includes(searchTerm)) ||
    //            (v.Producttype && v.Producttype.toLowerCase().includes(searchTerm)) ||
    //            (v.Brand && v.Brand.toLowerCase().includes(searchTerm)) ||
    //            (v.ServiceEngineer && v.ServiceEngineer.toLowerCase().includes(searchTerm)) ||
    //            (v.PrimarySegment && v.PrimarySegment.toLowerCase().includes(searchTerm)) ||
    //            (v.Secondarysegment && v.Secondarysegment.toLowerCase().includes(searchTerm)) ||
    //            (v.Operatorname && v.Operatorname.toLowerCase().includes(searchTerm)) ||
    //            (v.Machinestatus && v.Machinestatus.toLowerCase().includes(searchTerm)) ||
    //            (v.Cuurentsiteaddress && v.Cuurentsiteaddress.toLowerCase().includes(searchTerm)) ||
    //            (v.Remarks && v.Remarks.toLowerCase().includes(searchTerm)) ||
    //            (v.SaleszorderNumber && v.SaleszorderNumber.toLowerCase().includes(searchTerm)) ||
    //            (v.Nextservicetype && v.Nextservicetype.toLowerCase().includes(searchTerm)) ||
    //            (v.LastHMRupdateddate && new Date(parseInt(v.LastHMRupdateddate.substr(6))).toLocaleDateString().includes(searchTerm)) ||
    //            (v.Commissioningdate && new Date(parseInt(v.Commissioningdate.substr(6))).toLocaleDateString().includes(searchTerm)) ||
    //            (v.SaleDate && new Date(parseInt(v.SaleDate.substr(6))).toLocaleDateString().includes(searchTerm)) ||
    //            (v.Lastservicedate && new Date(parseInt(v.Lastservicedate.substr(6))).toLocaleDateString().includes(searchTerm)) ||
    //            (v.nextservicedate && new Date(parseInt(v.nextservicedate.substr(6))).toLocaleDateString().includes(searchTerm)));
    //    }
    //    currentPage = 1;
    //    renderVehicleList(filteredVehicles);
    //}

    $('#searchInput').on('keyup', function () {
        //currentPage = 1; // Reset to page 1 for every new search
        renderPage();
    });

    // Click on the content of a list item (not the checkbox)
    $(document).on('click', '.list-item-content', function () {
        const serial = $(this).parent().data('serial');
        $('#vehicleList .list-item').removeClass('active');
        $(this).parent().addClass('active');
        loadDetailView(serial, false);
    });

    $('#addVehicleBtn').on('click', function () {
        selectedVehicleSerial = null;
        const detailContainer = $('#detailViewContainer');
        $('#vehicleList .list-item').removeClass('active');
        $('#editBtn, #deleteBtn').prop('disabled', true);

        const formHtml = `
            <h4 style="margin-bottom:20px;">New Vehicle Details</h4>
            <form id="vehicleForm" class="vehicle-form-grid">
                          <div class="form-group">
    <label>Serial Number</label>
    <input type="text" name="SerialNumber" value="">
</div>
<div class="form-group">
    <label>Brand</label>
    <input type="text" name="Brand" value="">
</div>
<div class="form-group">
    <label>Model</label>
    <input type="text" name="Model" value="">
</div>
<div class="form-group">
    <label>Service Engineer</label>
    <input type="text" name="ServiceEngineer" value="">
</div>
<div class="form-group">
    <label>Primary Segment</label>
    <input type="text" name="PrimarySegment" value="">
</div>
<div class="form-group">
    <label>Sale Date</label>
    <input type="date" name="SaleDate" value="">
</div>
<div class="form-group">
    <label>Average HMR</label>
    <input type="number" step="0.01" name="AverageHMR" value="">
</div>
<div class="form-group">
    <label>Sales Order Number</label>
    <input type="text" name="SaleszorderNumber" value="">
</div>
<div class="form-group">
    <label>Next Service Type</label>
    <input type="text" name="Nextservicetype" value="">
</div>
<div class="form-group">
    <label>Last HMR Updated Date</label>
    <input type="date" name="LastHMRupdateddate" value="">
</div>
<div class="form-group">
    <label>Remarks</label>
    <input type="text" name="Remarks" value="">
</div>
<div class="form-group">
    <label>Operator Name</label>
    <input type="text" name="Operatorname" value="">
</div>
<div class="form-group">
    <label>Product Type</label>
    <input type="text" name="Producttype" value="">
</div>
<div class="form-group">
    <label>Machine Status</label>
    <input type="text" name="Machinestatus" value="">
</div>
<div class="form-group">
    <label>Secondary Segment</label>
    <input type="text" name="Secondarysegment" value="">
</div>
<div class="form-group">
    <label>Commissioning Date</label>
    <input type="date" name="Commissioningdate" value="">
</div>
<div class="form-group">
    <label>Machine HMR</label>
    <input type="number" name="MachineHMR" value="">
</div>
<div class="form-group">
    <label>Last Service Date</label>
    <input type="date" name="Lastservicedate" value="">
</div>
<div class="form-group">
    <label>Current Site Address</label>
    <input type="text" name="Cuurentsiteaddress" value="">
</div>
<div class="form-group">
    <label>Next Service Date</label>
    <input type="date" name="nextservicedate" value="">
</div>
<div class="form-group inputnew ">
    <label>Is Imported</label>
    <input type="checkbox" name="Isimported" value="true">
<div class="form-group inputnew">
    <label>Is Active</label>
    <input type="checkbox" name="Isactive" value="true">
</div>
<div id="form-actions" class="mt-3">
                 <button id="backBtn" class="action-btn">Back</button>
                 <button id="saveBtn" class="action-btn">Save New Vehicle</button>
            </div>
            </form>`;
        detailContainer.html(formHtml);
    });


    $('#editBtn').on('click', function () {
        if (selectedVehicleSerial) {
            loadDetailView(selectedVehicleSerial, true);
        } else {
            alert("Please select a vehicle from the list first.");
        }
    });

    $(document).on('click', '#backBtn', function () {
        if (selectedVehicleSerial) loadDetailView(selectedVehicleSerial, false);
        else { $('#detailViewContainer').html('<p class="placeholder-text">Select a vehicle.</p>'); }
    });

    // SAVE (for Create and Update) - Corrected to be a delegated event handler
    // This now makes only one call to the validation function
    $(document).on('submit', '#vehicleForm', function (e) {
        e.preventDefault();

        if (!validateForm()) {
            showToast("Please correct the errors before saving.", "error");
            return; // Stop if validation fails
        }


        const isUpdating = !!selectedVehicleSerial;
        const url = isUpdating ? '/Vehicle/UpdateVehicle' : '/Vehicle/CreateVehicle';

        $.ajax({
            url: url,
            type: 'POST',
            data: $(this).serialize(),
            success: function (response) {
                showToast(response.message, response.success ? 'success' : 'error');
                if (response.success) {
                    loadVehicleList();
                    if (isUpdating) {
                        loadDetailView(selectedVehicleSerial, false);
                    }
                }
            }
        });
    });

    // DELETE 
    $('#deleteBtn').on('click', function () {
        debugger
        if (!selectedVehicleSerial) {
            alert("No vehicle is selected to delete.");
            return;
        }

        if (confirm(`Are you sure you want to delete vehicle ${selectedVehicleSerial}?`)) {
            $.ajax({
                url: '/Vehicle/DeleteVehicle', // Your single delete C# action
                type: 'POST',
                data: { serialNumber: selectedVehicleSerial },
                success: function (response) {
                    showToast(response.message, response.success ? 'success' : 'error');
                    if (response.success) {
                        $('#detailViewContainer').html('<p class="placeholder-text">Select a vehicle.</p>');
                        selectedVehicleSerial = null;
                        $('#editBtn, #deleteSingleBtn').prop('disabled', true);
                        loadVehicleList();
                    }
                }
            });
        }
    });

    // 2. MULTI-DELETE: Triggered by the button in the left-hand panel
    $('#deleteSelectedBtn').on('click', function () {
        const selectedSerials = [];
        $('#vehicleList .vehicle-checkbox:checked').each(function () {
            selectedSerials.push($(this).data('serial'));
        });

        if (selectedSerials.length === 0) return;

        if (confirm(`Are you sure you want to delete ${selectedSerials.length} selected vehicle(s)?`)) {
            $.ajax({
                url: '/Vehicle/DeleteMultipleVehicles',
                type: 'POST',
                data: { serialNumbers: selectedSerials },
                traditional: true,
                success: function (response) {
                    showToast(response.message, response.success ? 'success' : 'error');
                    if (response.success) {
                        // If the currently viewed vehicle was deleted, clear the right panel
                        if (selectedSerials.includes(selectedVehicleSerial)) {
                            $('#detailViewContainer').html('<p class="placeholder-text">Select a vehicle.</p>');
                            selectedVehicleSerial = null;
                            $('#editBtn, #deleteSingleBtn').prop('disabled', true);
                        }
                        loadVehicleList();
                    }
                }
            });
        }
    });

    // --- INITIAL LOAD ---
    loadVehicleList();
});