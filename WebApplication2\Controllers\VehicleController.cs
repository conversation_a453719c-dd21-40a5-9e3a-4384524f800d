﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web.Mvc;
using WebApplication2.Models;

namespace WebApplication2.Controllers
{
    public class VehicleController : Controller
    {
        //public ActionResult Vehicle()
        //{
        //    return View();
        //}
        public ActionResult VehicleForm()
        {

            return PartialView();
        }
        public ActionResult Index()
        {
            return View();
        }
        //public ActionResult NEWW()
        //{
        //    return View();
        //}


        private string GetConnectionString()
        {
            return ConfigurationManager.ConnectionStrings["ALIEN"].ConnectionString;
        }

        [HttpGet]
        public JsonResult GetVehicleSummaryList()
        {
            var jsonResult = new JsonResult();
            try
            {
                var vehicles = new List<Vehicle>();
                using (SqlConnection con = new SqlConnection(GetConnectionString()))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_GetVehicleSummaryList", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        con.Open();
                        using (SqlDataReader rdr = cmd.ExecuteReader())
                        {
                            while (rdr.Read())
                            {
                                var vehicle = new Vehicle
                                {
                                    // Added DBNull checks to prevent crashes from NULL data
                                    SerialNumber = rdr["SerialNumber"] != DBNull.Value ? rdr["SerialNumber"].ToString() : "",
                                    Model = rdr["Model"] != DBNull.Value ? rdr["Model"].ToString() : "",
                                    nextservicedate = rdr["nextservicedate"] != DBNull.Value ? (DateTimeOffset)rdr["nextservicedate"] : DateTimeOffset.MinValue,
                                    Producttype = rdr["Producttype"] != DBNull.Value ? rdr["Producttype"].ToString() : "",
                                    Isactive = rdr["Isactive"] != DBNull.Value ? Convert.ToBoolean(rdr["Isactive"]) : false,

                                };
                                //    vehicle.ActivityStatus = $"Active: {(vehicle.Isactive ? "Yes" : "No")}, Imported: {(vehicle.Isimported ? "Yes" : "No")}";
                                vehicles.Add(vehicle);
                            }
                        }
                    }
                }
                jsonResult = new JsonResult
                {
                    Data = vehicles,
                    JsonRequestBehavior = JsonRequestBehavior.AllowGet,
                    MaxJsonLength = int.MaxValue // Set to the maximum possible value
                };
            }
            catch (Exception ex)
            {
                common.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return jsonResult;
            //return Json(vehicles, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        public JsonResult GetVehicleDetails()
        {
            var jsonResult = new JsonResult();

            Vehicle vehicle = null;
            var vehicless  = new List<Vehicle>();
            using (SqlConnection con = new SqlConnection(GetConnectionString()))
            {
                using (SqlCommand cmd = new SqlCommand("Getalldetails", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    //cmd.Parameters.AddWithValue("@SerialNumber", serialNumber);
                    con.Open();
                    using (SqlDataReader rdr = cmd.ExecuteReader())
                    {
                        while (rdr.Read())
                        {
                            vehicle = new Vehicle
                            {
                                SerialNumber = rdr["SerialNumber"] != DBNull.Value ? rdr["SerialNumber"].ToString() : "",
                                Brand = rdr["Brand"] != DBNull.Value ? rdr["Brand"].ToString() : "",
                                Model = rdr["Model"] != DBNull.Value ? rdr["Model"].ToString() : "",
                                ServiceEngineer = rdr["ServiceEngineer"] != DBNull.Value ? rdr["ServiceEngineer"].ToString() : "",
                                PrimarySegment = rdr["PrimarySegment"] != DBNull.Value ? rdr["PrimarySegment"].ToString() : "",
                                SaleDate = rdr["SaleDate"] != DBNull.Value ? (DateTimeOffset)rdr["SaleDate"] : DateTimeOffset.MinValue,
                                AverageHMR = rdr["AverageHMR"] != DBNull.Value ? Convert.ToDecimal(rdr["AverageHMR"]) : 0,
                                SaleszorderNumber = rdr["SaleszorderNumber"] != DBNull.Value ? rdr["SaleszorderNumber"].ToString() : "",
                                Nextservicetype = rdr["Nextservicetype"] != DBNull.Value ? rdr["Nextservicetype"].ToString() : "",
                                Isactive = rdr["Isactive"] != DBNull.Value ? Convert.ToBoolean(rdr["Isactive"]) : false,
                                LastHMRupdateddate = rdr["LastHMRupdateddate"] != DBNull.Value ? (DateTimeOffset)rdr["LastHMRupdateddate"] : DateTimeOffset.MinValue,
                                Isimported = rdr["Isimported"] != DBNull.Value ? Convert.ToBoolean(rdr["Isimported"]) : false,
                                Remarks = rdr["Remarks"] != DBNull.Value ? rdr["Remarks"].ToString() : "",
                                Operatorname = rdr["Operatorname"] != DBNull.Value ? rdr["Operatorname"].ToString() : "",
                                Producttype = rdr["Producttype"] != DBNull.Value ? rdr["Producttype"].ToString() : "",
                                Machinestatus = rdr["Machinestatus"] != DBNull.Value ? rdr["Machinestatus"].ToString() : "",
                                Secondarysegment = rdr["Secondarysegment"] != DBNull.Value ? rdr["Secondarysegment"].ToString() : "",
                                Commissioningdate = rdr["Commissioningdate"] != DBNull.Value ? (DateTimeOffset)rdr["Commissioningdate"] : DateTimeOffset.MinValue,
                                MachineHMR = rdr["MachineHMR"] != DBNull.Value ? Convert.ToInt16(rdr["MachineHMR"]) : (short)0,
                                Lastservicedate = rdr["Lastservicedate"] != DBNull.Value ? (DateTimeOffset)rdr["Lastservicedate"] : DateTimeOffset.MinValue,
                                Cuurentsiteaddress = rdr["Cuurentsiteaddress"] != DBNull.Value ? rdr["Cuurentsiteaddress"].ToString() : "",
                                nextservicedate = rdr["nextservicedate"] != DBNull.Value ? (DateTimeOffset)rdr["nextservicedate"] : DateTimeOffset.MinValue,
                                WarrantyStatus = (rdr["SaleDate"] != DBNull.Value && (DateTimeOffset)rdr["SaleDate"] < DateTimeOffset.Now.AddYears(-5)) 
    ? "Vehicle is NOT under warranty" 
    : "Vehicle is under warranty"                         
                            };


                            vehicless.Add(vehicle);


                        }
                    }
                }
            }

            jsonResult = new JsonResult
            {
                Data = vehicless,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
                MaxJsonLength = int.MaxValue // Set to the maximum possible value
            };
            return jsonResult;
        }

        [HttpPost]
        public JsonResult CreateVehicle(Vehicle vehicle)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(GetConnectionString()))
                {
                    using (SqlCommand cmd = new SqlCommand("CreatenewVehicledata", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SerialNumber", vehicle.SerialNumber);
                        cmd.Parameters.AddWithValue("@Brand", vehicle.Brand);
                        cmd.Parameters.AddWithValue("@Model", vehicle.Model);
                        //cmd.Parameters.AddWithValue("@SaleDate", vehicle.SaleDate);
                        cmd.Parameters.AddWithValue("@ServiceEngineer", vehicle.ServiceEngineer);
                        cmd.Parameters.AddWithValue("@PrimarySegment", vehicle.PrimarySegment);                 
                        cmd.Parameters.AddWithValue("@AverageHMR", vehicle.AverageHMR);
                        cmd.Parameters.AddWithValue("@SaleszorderNumber", vehicle.SaleszorderNumber);
                        cmd.Parameters.AddWithValue("@Nextservicetype", vehicle.Nextservicetype);
                        cmd.Parameters.AddWithValue("@Isactive", vehicle.Isactive);
                        //cmd.Parameters.AddWithValue("@LastHMRupdateddate", vehicle.LastHMRupdateddate);
                        cmd.Parameters.AddWithValue("@Isimported", vehicle.Isimported);
                        cmd.Parameters.AddWithValue("@Remarks", vehicle.Remarks);
                        cmd.Parameters.AddWithValue("@Operatorname", vehicle.Operatorname);
                        cmd.Parameters.AddWithValue("@Producttype", vehicle.Producttype);
                        cmd.Parameters.AddWithValue("@Machinestatus", vehicle.Machinestatus);
                        cmd.Parameters.AddWithValue("@Secondarysegment", vehicle.Secondarysegment);
                        //cmd.Parameters.AddWithValue("@Commissioningdate", vehicle.Commissioningdate);
                        cmd.Parameters.AddWithValue("@MachineHMR", vehicle.MachineHMR);
                        //cmd.Parameters.AddWithValue("@Lastservicedate", vehicle.Lastservicedate);
                        cmd.Parameters.AddWithValue("@Cuurentsiteaddress", vehicle.Cuurentsiteaddress);
                        //cmd.Parameters.AddWithValue("@nextservicedate", vehicle.nextservicedate);
                        if (vehicle.SaleDate == DateTimeOffset.MinValue)
                        {
                            cmd.Parameters.AddWithValue("@SaleDate", DBNull.Value);
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@SaleDate", vehicle.SaleDate);
                        }
                        if(vehicle.LastHMRupdateddate == DateTimeOffset.MinValue)
                        {   cmd.Parameters.AddWithValue("@LastHMRupdateddate", DBNull.Value);
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@LastHMRupdateddate", vehicle.LastHMRupdateddate);
                        }
                        if (vehicle.nextservicedate == DateTimeOffset.MinValue)
                        {
                            cmd.Parameters.AddWithValue("@nextservicedate", DBNull.Value);
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@nextservicedate", vehicle.nextservicedate);
                        }
                        if (vehicle.Commissioningdate == DateTimeOffset.MinValue)
                        {
                            cmd.Parameters.AddWithValue("@Commissioningdate", DBNull.Value);
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@Commissioningdate", vehicle.Commissioningdate);
                        }
                        if (vehicle.Lastservicedate == DateTimeOffset.MinValue)
                        {
                            cmd.Parameters.AddWithValue("@Lastservicedate", DBNull.Value);
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@Lastservicedate", vehicle.Lastservicedate);
                        }
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
                return Json(new { success = true, message = "Vehicle created successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        public JsonResult UpdateVehicle(Vehicle vehicle)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(GetConnectionString()))
                {
                    using (SqlCommand cmd = new SqlCommand("UpdateVehicledata", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SerialNumber", vehicle.SerialNumber);
                        cmd.Parameters.AddWithValue("@Brand", vehicle.Brand);
                        cmd.Parameters.AddWithValue("@Model", vehicle.Model);
                        cmd.Parameters.AddWithValue("@ServiceEngineer", vehicle.ServiceEngineer);
                        cmd.Parameters.AddWithValue("@PrimarySegment", vehicle.PrimarySegment);
                        cmd.Parameters.AddWithValue("@SaleDate", vehicle.SaleDate);
                        cmd.Parameters.AddWithValue("@AverageHMR", vehicle.AverageHMR);
                        cmd.Parameters.AddWithValue("@SaleszorderNumber", vehicle.SaleszorderNumber);
                        cmd.Parameters.AddWithValue("@Nextservicetype", vehicle.Nextservicetype);
                        cmd.Parameters.AddWithValue("@Isactive", vehicle.Isactive);
                        cmd.Parameters.AddWithValue("@LastHMRupdateddate", vehicle.LastHMRupdateddate);
                        cmd.Parameters.AddWithValue("@Isimported", vehicle.Isimported);
                        cmd.Parameters.AddWithValue("@Remarks", vehicle.Remarks);
                        cmd.Parameters.AddWithValue("@Operatorname", vehicle.Operatorname);
                        cmd.Parameters.AddWithValue("@Producttype", vehicle.Producttype);
                        cmd.Parameters.AddWithValue("@Machinestatus", vehicle.Machinestatus);
                        cmd.Parameters.AddWithValue("@Secondarysegment", vehicle.Secondarysegment);
                        cmd.Parameters.AddWithValue("@Commissioningdate", vehicle.Commissioningdate);
                        cmd.Parameters.AddWithValue("@MachineHMR", vehicle.MachineHMR);
                        cmd.Parameters.AddWithValue("@Lastservicedate", vehicle.Lastservicedate);
                        cmd.Parameters.AddWithValue("@Cuurentsiteaddress", vehicle.Cuurentsiteaddress);
                        cmd.Parameters.AddWithValue("@nextservicedate", vehicle.nextservicedate);
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
                return Json(new { success = true, message = "Vehicle updated successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        public ActionResult DeleteVehicle(string serialNumber)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(GetConnectionString()))
                {
                    using (SqlCommand cmd = new SqlCommand("Deletevehiclesingle", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SerialNumber", serialNumber);
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
                return Json(new { success = true, message = "Vehicle deleted successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        public JsonResult DeleteMultipleVehicles(List<string> serialNumbers)
        {
            if (serialNumbers == null || !serialNumbers.Any())
            {
                return Json(new { success = false, message = "No vehicles selected." });
            }
            try
            {
                string constr = GetConnectionString(); 
                using (SqlConnection con = new SqlConnection(constr))
                {
                    
                    string query = "DELETE FROM Vehicles WHERE SerialNumber IN (@SerialNumbers)";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        
                        cmd.Parameters.AddWithValue("@SerialNumbers", string.Join(",", serialNumbers));
                        con.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
                return Json(new { success = true, message = "Selected vehicles deleted successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
} 