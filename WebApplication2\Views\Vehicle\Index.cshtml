﻿@{ Layout = null; }
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Vehicle Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="~/Assets/css/new-styles.css" rel="stylesheet" />
</head>
<body>
    <div class="main-container">
        <header class="main-header">
            <h3>Vehicle Management</h3>
            <button id="addVehicleBtn" class="add-btn">Add New Vehicle</button>
        </header>

        <div class="controls-bar">
            <div class="filter-pills" id="statusFilterContainer">
            </div>
            <div class="view-toggles">
                <button class="view-btn active" data-view="list">List View</button>
                <button class="view-btn" data-view="table">Table View</button>
                <button class="view-btn" data-view="card">Card View</button>
            </div>
        </div>

        <main id="dataContainer" class="data-container">
        </main>

        <footer id="paginationContainer" class="pagination-footer"></footer>
    </div>

    <div class="modal-overlay" id="vehicleModal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 id="modalTitle"></h5>
                <button id="closeModalBtn" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
            </div>
            <div class="modal-footer">
                <button type="button" id="cancelModalBtn" class="action-btn">Close</button>
                <button type="submit" form="vehicleForm" id="saveBtn" class="action-btn">Save</button>
            </div>
        </div>
    </div>

    <div id="toastContainer"></div>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="~/Assets/js/new-vehicles.js"></script>
</body>
</html>